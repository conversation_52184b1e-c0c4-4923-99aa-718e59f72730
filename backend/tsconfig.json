{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": false, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "sourceMap": true, "baseUrl": "./src", "paths": {"@/*": ["*"], "@/types/*": ["types/*"], "@/models/*": ["models/*"], "@/controllers/*": ["controllers/*"], "@/services/*": ["services/*"], "@/middleware/*": ["middleware/*"], "@/utils/*": ["utils/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "tests", "**/*.test.ts", "**/*.spec.ts"]}