import { Request, Response } from 'express';
import { User } from '../../models/users/User';
import { ActivityLog } from '../../models/users/ActivityLog';
import { sessionService } from '../../services/auth/session';
import { 
  RegisterRequest, 
  LoginRequest, 
  LoginResponse,
  RefreshTokenRequest,
  LogoutRequest,
  AuthenticatedRequest
} from '../../types/auth';
import { logger } from '../../utils/logger';
import { getClientIP, parseUserAgent } from '../../utils/request';
import { validateRegistration, validateLogin } from '../../validators/auth';
// import { sendWelcomeEmail } from '../../services/email';

export class AuthController {
  public async register(req: Request, res: Response): Promise<void> {
    try {
      const body: RegisterRequest = req.body;
      
      // Validate input
      const validation = validateRegistration(body);
      if (!validation.isValid) {
        res.status(400).json({
          error: 'Validation failed',
          code: 'VALIDATION_ERROR',
          details: validation.errors
        });
        return;
      }

      // Check if user already exists
      const existingUser = await User.findByEmail(body.email);
      if (existingUser) {
        res.status(409).json({
          error: 'User already exists',
          code: 'USER_EXISTS'
        });
        return;
      }

      // Create user with default settings
      const user = new User({
        profile: {
          firstName: body.firstName,
          lastName: body.lastName,
          email: body.email.toLowerCase(),
          company: body.company,
          address: body.address,
          timezone: body.timezone || 'America/New_York',
          language: body.language || 'en-US',
          createdAt: new Date()
        },
        security: {
          passwordHash: body.password, // Will be hashed by pre-save middleware
          passwordLastChanged: new Date(),
          twoFactorEnabled: false,
          loginNotifications: true,
          suspiciousActivityAlerts: true,
          failedLoginAttempts: 0
        },
        billing: {
          billingAddress: body.address,
          taxInformation: {
            taxExempt: false
          },
          invoicePreferences: {
            emailInvoices: true,
            paperInvoices: false,
            autoPayEnabled: false,
            paymentTerms: 15
          },
          subscriptionPreferences: {
            autoRenew: true,
            renewalReminders: true,
            upgradeNotifications: true
          }
        },
        notifications: {
          email: {
            serviceUpdates: true,
            maintenanceAlerts: true,
            billingReminders: true,
            invoiceNotifications: true,
            ticketUpdates: true,
            securityAlerts: true,
            marketingEmails: false,
            weeklyReports: true
          },
          sms: {
            enabled: false,
            criticalAlerts: true,
            serviceDowntime: true,
            billingIssues: true,
            securityAlerts: true
          },
          push: {
            enabled: true,
            serviceUpdates: true,
            ticketResponses: true,
            billingReminders: false
          },
          frequency: {
            digest: 'daily',
            quietHours: {
              enabled: true,
              start: '22:00',
              end: '08:00'
            }
          }
        },
        preferences: {
          theme: 'dark',
          compactMode: false,
          showWelcomeMessage: true,
          defaultDashboardView: 'overview',
          widgetPreferences: {
            showStats: true,
            showRecentActivity: true,
            showQuickActions: true,
            showSystemStatus: true
          },
          tablePreferences: {
            itemsPerPage: 25,
            defaultSort: 'created_desc',
            compactTables: false
          }
        },
        role: 'user',
        status: 'pending', // Will be activated after email verification
        emailVerified: false
      });

      await user.save();

      // Log registration activity
      await ActivityLog.logActivity({
        userId: user._id,
        type: 'profile_update',
        description: 'User account created',
        details: {
          registrationMethod: 'email',
          company: body.company?.name
        },
        ipAddress: getClientIP(req),
        userAgent: req.headers['user-agent'] || '',
        severity: 'low'
      });

      // Send welcome email (async) - TODO: Implement email service
      // sendWelcomeEmail(user.profile.email, user.profile.firstName).catch(error => {
      //   logger.error('Failed to send welcome email', { error, userId: user._id.toString() });
      // });

      logger.info('User registered successfully', {
        userId: user._id.toString(),
        email: user.profile.email,
        ipAddress: getClientIP(req)
      });

      res.status(201).json({
        message: 'Registration successful',
        user: {
          id: user._id.toString(),
          email: user.profile.email,
          firstName: user.profile.firstName,
          lastName: user.profile.lastName,
          status: user.status,
          emailVerified: user.emailVerified
        }
      });
    } catch (error) {
      logger.error('Registration failed', { error, body: req.body });
      res.status(500).json({
        error: 'Registration failed',
        code: 'REGISTRATION_ERROR'
      });
    }
  }

  public async login(req: Request, res: Response): Promise<void> {
    try {
      const body: LoginRequest = req.body;
      
      // Validate input
      const validation = validateLogin(body);
      if (!validation.isValid) {
        res.status(400).json({
          error: 'Validation failed',
          code: 'VALIDATION_ERROR',
          details: validation.errors
        });
        return;
      }

      // Find user
      const user = await User.findByEmail(body.email);
      if (!user) {
        res.status(401).json({
          error: 'Invalid credentials',
          code: 'INVALID_CREDENTIALS'
        });
        return;
      }

      // Check if account is locked
      if (user.isLocked()) {
        res.status(423).json({
          error: 'Account temporarily locked due to failed login attempts',
          code: 'ACCOUNT_LOCKED'
        });
        return;
      }

      // Verify password
      const isPasswordValid = await user.comparePassword(body.password);
      if (!isPasswordValid) {
        await user.incrementFailedLogins();
        
        // Log failed login attempt
        await ActivityLog.logActivity({
          userId: user._id,
          type: 'login',
          description: 'Failed login attempt',
          details: {
            reason: 'Invalid password',
            attempts: user.security.failedLoginAttempts + 1
          },
          ipAddress: getClientIP(req),
          userAgent: req.headers['user-agent'] || '',
          severity: 'high'
        });

        res.status(401).json({
          error: 'Invalid credentials',
          code: 'INVALID_CREDENTIALS'
        });
        return;
      }

      // Check account status
      if (user.status !== 'active') {
        res.status(403).json({
          error: 'Account not active',
          code: 'ACCOUNT_INACTIVE',
          status: user.status
        });
        return;
      }

      // Reset failed login attempts
      await user.resetFailedLogins();

      // Parse user agent
      const userAgent = parseUserAgent(req.headers['user-agent'] || '');

      // Create session
      const { session, tokens } = await sessionService.createSession({
        userId: user._id,
        deviceName: body.deviceName || userAgent.device,
        browser: userAgent.browser,
        ipAddress: getClientIP(req),
        userAgent: req.headers['user-agent'] || '',
        rememberMe: body.rememberMe
      });

      // Update last login
      user.profile.lastLoginAt = new Date();
      await user.save();

      // Log successful login
      await ActivityLog.logActivity({
        userId: user._id,
        type: 'login',
        description: 'Successful login',
        details: {
          deviceName: session.deviceName,
          browser: session.browser,
          rememberMe: body.rememberMe
        },
        ipAddress: getClientIP(req),
        userAgent: req.headers['user-agent'] || '',
        severity: 'low'
      });

      const response: LoginResponse = {
        user: {
          id: user._id.toString(),
          email: user.profile.email,
          firstName: user.profile.firstName,
          lastName: user.profile.lastName,
          role: user.role,
          emailVerified: user.emailVerified
        },
        tokens,
        session: {
          id: session.id,
          deviceName: session.deviceName,
          expiresAt: session.expiresAt.toISOString()
        }
      };

      logger.info('User logged in successfully', {
        userId: user._id.toString(),
        email: user.profile.email,
        sessionId: session.id,
        ipAddress: getClientIP(req)
      });

      res.json(response);
    } catch (error) {
      logger.error('Login failed', { error, body: req.body });
      res.status(500).json({
        error: 'Login failed',
        code: 'LOGIN_ERROR'
      });
    }
  }

  public async refresh(req: Request, res: Response): Promise<void> {
    try {
      const body: RefreshTokenRequest = req.body;
      
      if (!body.refreshToken) {
        res.status(400).json({
          error: 'Refresh token required',
          code: 'REFRESH_TOKEN_MISSING'
        });
        return;
      }

      const result = await sessionService.refreshSession(body.refreshToken);
      if (!result) {
        res.status(401).json({
          error: 'Invalid or expired refresh token',
          code: 'REFRESH_TOKEN_INVALID'
        });
        return;
      }

      res.json(result);
    } catch (error) {
      logger.error('Token refresh failed', { error });
      res.status(500).json({
        error: 'Token refresh failed',
        code: 'REFRESH_ERROR'
      });
    }
  }

  public async logout(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const body: LogoutRequest = req.body;
      const currentSessionId = req.user.sessionId;

      if (body.allSessions) {
        // Logout from all sessions
        const revokedCount = await sessionService.revokeAllSessions(req.user.id);
        
        // Log activity
        await ActivityLog.logActivity({
          userId: req.user.id as any,
          type: 'logout',
          description: 'Logged out from all sessions',
          details: { revokedSessions: revokedCount },
          ipAddress: getClientIP(req),
          userAgent: req.headers['user-agent'] || '',
          severity: 'low'
        });

        res.json({ 
          message: 'Logged out from all sessions',
          revokedSessions: revokedCount
        });
      } else {
        // Logout from current session
        const sessionId = body.sessionId || currentSessionId;
        const success = await sessionService.revokeSession(sessionId, req.user.id);
        
        if (!success) {
          res.status(404).json({
            error: 'Session not found',
            code: 'SESSION_NOT_FOUND'
          });
          return;
        }

        // Log activity
        await ActivityLog.logActivity({
          userId: req.user.id as any,
          type: 'logout',
          description: 'Logged out',
          details: { sessionId },
          ipAddress: getClientIP(req),
          userAgent: req.headers['user-agent'] || '',
          severity: 'low'
        });

        res.json({ message: 'Logged out successfully' });
      }

      logger.info('User logged out', {
        userId: req.user.id,
        allSessions: body.allSessions,
        ipAddress: getClientIP(req)
      });
    } catch (error) {
      logger.error('Logout failed', { error, userId: req.user?.id });
      res.status(500).json({
        error: 'Logout failed',
        code: 'LOGOUT_ERROR'
      });
    }
  }
}
