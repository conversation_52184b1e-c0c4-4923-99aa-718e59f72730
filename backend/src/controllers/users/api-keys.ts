import { Response } from 'express';
import { Api<PERSON><PERSON> } from '../../models/users/ApiKey';
import { ActivityLog } from '../../models/users/ActivityLog';
import { AuthenticatedRequest, CreateApiKeyRequest } from '../../types/auth';
import { ApiKeyResponse } from '../../types/database/user';
import { API_PERMISSIONS } from '../../types/auth';
import { logger } from '../../utils/logger';
import { getClientIP } from '../../utils/request';

export class ApiKeysController {
  private static readonly MAX_API_KEYS_PER_USER = 10;

  public async getApiKeys(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const apiKeys = await ApiKey.findActiveByUserId(req.user.id);
      
      const apiKeyResponses: ApiKeyResponse[] = apiKeys.map(key => ({
        id: key._id.toString(),
        name: key.name,
        keyPrefix: key.keyPrefix,
        permissions: key.permissions,
        lastUsedAt: key.lastUsedAt?.toISOString(),
        expiresAt: key.expiresAt?.toISOString(),
        isActive: key.isActive,
        createdAt: key.createdAt.toISOString()
      }));

      res.json({
        apiKeys: apiKeyResponses,
        total: apiKeyResponses.length,
        maxAllowed: ApiKeysController.MAX_API_KEYS_PER_USER
      });
    } catch (error) {
      logger.error('Failed to get API keys', { error, userId: req.user.id });
      res.status(500).json({
        error: 'Failed to get API keys',
        code: 'API_KEYS_ERROR'
      });
    }
  }

  public async createApiKey(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const body: CreateApiKeyRequest = req.body;
      
      // Validate input
      if (!body.name || !body.name.trim()) {
        res.status(400).json({
          error: 'API key name is required',
          code: 'VALIDATION_ERROR'
        });
        return;
      }

      if (!body.permissions || body.permissions.length === 0) {
        res.status(400).json({
          error: 'At least one permission is required',
          code: 'VALIDATION_ERROR'
        });
        return;
      }

      // Validate permissions
      const validPermissions = Object.keys(API_PERMISSIONS);
      const invalidPermissions = body.permissions.filter(p => !validPermissions.includes(p));
      if (invalidPermissions.length > 0) {
        res.status(400).json({
          error: 'Invalid permissions',
          code: 'INVALID_PERMISSIONS',
          invalidPermissions,
          validPermissions
        });
        return;
      }

      // Check if user has reached the limit
      const currentCount = await ApiKey.countActiveByUserId(req.user.id);
      if (currentCount >= ApiKeysController.MAX_API_KEYS_PER_USER) {
        res.status(429).json({
          error: `Maximum of ${ApiKeysController.MAX_API_KEYS_PER_USER} API keys allowed`,
          code: 'API_KEY_LIMIT_EXCEEDED'
        });
        return;
      }

      // Check for duplicate names
      const existingKeys = await ApiKey.findActiveByUserId(req.user.id);
      if (existingKeys.some(key => key.name.toLowerCase() === body.name.trim().toLowerCase())) {
        res.status(409).json({
          error: 'API key name already exists',
          code: 'API_KEY_NAME_EXISTS'
        });
        return;
      }

      // Generate API key
      const { key, prefix } = ApiKey.generateKey();
      
      // Parse expiry date if provided
      let expiresAt: Date | undefined;
      if (body.expiresAt) {
        expiresAt = new Date(body.expiresAt);
        if (isNaN(expiresAt.getTime()) || expiresAt <= new Date()) {
          res.status(400).json({
            error: 'Invalid expiry date',
            code: 'INVALID_EXPIRY_DATE'
          });
          return;
        }
      }

      // Create API key
      const apiKey = new ApiKey({
        userId: req.user.id,
        name: body.name.trim(),
        keyHash: key, // Will be hashed by pre-save middleware
        keyPrefix: prefix,
        permissions: body.permissions,
        expiresAt,
        isActive: true
      });

      await apiKey.save();

      // Log API key creation
      await ActivityLog.logActivity({
        userId: req.user.id as any,
        type: 'security_event',
        description: 'API key created',
        details: {
          apiKeyId: apiKey._id.toString(),
          name: apiKey.name,
          permissions: apiKey.permissions,
          expiresAt: expiresAt?.toISOString()
        },
        ipAddress: getClientIP(req),
        userAgent: req.headers['user-agent'] || '',
        severity: 'medium'
      });

      logger.info('API key created', {
        userId: req.user.id,
        apiKeyId: apiKey._id.toString(),
        name: apiKey.name,
        permissions: apiKey.permissions,
        ipAddress: getClientIP(req)
      });

      const response: ApiKeyResponse = {
        id: apiKey._id.toString(),
        name: apiKey.name,
        key, // Only returned on creation
        keyPrefix: apiKey.keyPrefix,
        permissions: apiKey.permissions,
        expiresAt: apiKey.expiresAt?.toISOString(),
        isActive: apiKey.isActive,
        createdAt: apiKey.createdAt.toISOString()
      };

      res.status(201).json({
        message: 'API key created successfully',
        apiKey: response,
        warning: 'This is the only time the full API key will be shown. Please save it securely.'
      });
    } catch (error) {
      logger.error('Failed to create API key', { error, userId: req.user.id });
      res.status(500).json({
        error: 'Failed to create API key',
        code: 'API_KEY_CREATE_ERROR'
      });
    }
  }

  public async revokeApiKey(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { id: apiKeyId } = req.params;
      
      if (!apiKeyId) {
        res.status(400).json({
          error: 'API key ID required',
          code: 'API_KEY_ID_REQUIRED'
        });
        return;
      }

      const apiKey = await ApiKey.findOne({
        _id: apiKeyId,
        userId: req.user.id,
        isActive: true
      });

      if (!apiKey) {
        res.status(404).json({
          error: 'API key not found',
          code: 'API_KEY_NOT_FOUND'
        });
        return;
      }

      await apiKey.deactivate();

      // Log API key revocation
      await ActivityLog.logActivity({
        userId: req.user.id as any,
        type: 'security_event',
        description: 'API key revoked',
        details: {
          apiKeyId: apiKey._id.toString(),
          name: apiKey.name,
          permissions: apiKey.permissions
        },
        ipAddress: getClientIP(req),
        userAgent: req.headers['user-agent'] || '',
        severity: 'medium'
      });

      logger.info('API key revoked', {
        userId: req.user.id,
        apiKeyId: apiKey._id.toString(),
        name: apiKey.name,
        ipAddress: getClientIP(req)
      });

      res.json({
        message: 'API key revoked successfully',
        apiKeyId: apiKey._id.toString(),
        name: apiKey.name
      });
    } catch (error) {
      logger.error('Failed to revoke API key', { error, userId: req.user.id, apiKeyId: req.params.id });
      res.status(500).json({
        error: 'Failed to revoke API key',
        code: 'API_KEY_REVOKE_ERROR'
      });
    }
  }

  public async revokeAllApiKeys(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const result = await ApiKey.revokeAllForUser(req.user.id);
      
      // Log mass API key revocation
      await ActivityLog.logActivity({
        userId: req.user.id as any,
        type: 'security_event',
        description: 'All API keys revoked',
        details: {
          revokedCount: result.modifiedCount
        },
        ipAddress: getClientIP(req),
        userAgent: req.headers['user-agent'] || '',
        severity: 'high'
      });

      logger.info('All API keys revoked', {
        userId: req.user.id,
        revokedCount: result.modifiedCount,
        ipAddress: getClientIP(req)
      });

      res.json({
        message: `${result.modifiedCount || 0} API key${(result.modifiedCount || 0) !== 1 ? 's' : ''} revoked successfully`,
        revokedCount: result.modifiedCount || 0
      });
    } catch (error) {
      logger.error('Failed to revoke all API keys', { error, userId: req.user.id });
      res.status(500).json({
        error: 'Failed to revoke API keys',
        code: 'API_KEYS_REVOKE_ERROR'
      });
    }
  }

  public async getApiKeyStats(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const apiKeys = await ApiKey.findActiveByUserId(req.user.id);
      
      // Calculate stats
      const totalKeys = apiKeys.length;
      const usedKeys = apiKeys.filter(key => key.lastUsedAt).length;
      const expiredKeys = apiKeys.filter(key => key.isExpired()).length;
      const expiringKeys = apiKeys.filter(key => {
        if (!key.expiresAt) return false;
        const thirtyDaysFromNow = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);
        return key.expiresAt <= thirtyDaysFromNow && !key.isExpired();
      }).length;

      // Permission usage stats
      const permissionUsage: Record<string, number> = {};
      apiKeys.forEach(key => {
        key.permissions.forEach(permission => {
          permissionUsage[permission] = (permissionUsage[permission] || 0) + 1;
        });
      });

      // Recent API access
      const recentApiAccess = await ActivityLog.findRecentByType(req.user.id, 'api_access', 168); // Last 7 days

      res.json({
        totalKeys,
        usedKeys,
        expiredKeys,
        expiringKeys,
        maxAllowed: ApiKeysController.MAX_API_KEYS_PER_USER,
        permissionUsage,
        recentApiAccessCount: recentApiAccess.length
      });
    } catch (error) {
      logger.error('Failed to get API key stats', { error, userId: req.user.id });
      res.status(500).json({
        error: 'Failed to get API key statistics',
        code: 'API_KEY_STATS_ERROR'
      });
    }
  }

  public async getAvailablePermissions(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const permissions = Object.entries(API_PERMISSIONS).map(([key, description]) => ({
        key,
        description,
        category: key.split(':')[0] // Extract category from permission key
      }));

      // Group by category
      const groupedPermissions = permissions.reduce((acc, permission) => {
        const category = permission.category;
        if (!acc[category]) {
          acc[category] = [];
        }
        acc[category].push(permission);
        return acc;
      }, {} as Record<string, typeof permissions>);

      res.json({
        permissions: groupedPermissions,
        total: permissions.length
      });
    } catch (error) {
      logger.error('Failed to get available permissions', { error });
      res.status(500).json({
        error: 'Failed to get available permissions',
        code: 'PERMISSIONS_ERROR'
      });
    }
  }
}
