import { Response } from 'express';
import { ActivityLog } from '../../models/users/ActivityLog';
import { AuthenticatedRequest } from '../../types/auth';
import { ActivityLogResponse } from '../../types/database/user';
import { logger } from '../../utils/logger';

export class ActivityController {
  public async getActivity(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const {
        type,
        severity,
        limit = 50,
        skip = 0,
        startDate,
        endDate
      } = req.query;

      // Parse dates
      let parsedStartDate: Date | undefined;
      let parsedEndDate: Date | undefined;

      if (startDate) {
        parsedStartDate = new Date(startDate as string);
        if (isNaN(parsedStartDate.getTime())) {
          res.status(400).json({
            error: 'Invalid start date format',
            code: 'INVALID_START_DATE'
          });
          return;
        }
      }

      if (endDate) {
        parsedEndDate = new Date(endDate as string);
        if (isNaN(parsedEndDate.getTime())) {
          res.status(400).json({
            error: 'Invalid end date format',
            code: 'INVALID_END_DATE'
          });
          return;
        }
      }

      // Validate limit and skip
      const parsedLimit = Math.min(parseInt(limit as string) || 50, 100); // Max 100 items
      const parsedSkip = Math.max(parseInt(skip as string) || 0, 0);

      // Get activity logs
      const logs = await ActivityLog.findByUserId(req.user.id, {
        type: type as string,
        severity: severity as string,
        limit: parsedLimit,
        skip: parsedSkip,
        startDate: parsedStartDate,
        endDate: parsedEndDate
      });

      // Transform to response format
      const activityResponses: ActivityLogResponse[] = logs.map(log => ({
        id: log._id.toString(),
        type: log.type,
        description: log.description,
        details: log.details,
        ipAddress: log.ipAddress,
        userAgent: log.userAgent,
        location: log.location,
        severity: log.severity,
        timestamp: log.createdAt.toISOString()
      }));

      // Get total count for pagination
      const totalQuery = ActivityLog.findByUserId(req.user.id, {
        type: type as string,
        severity: severity as string,
        startDate: parsedStartDate,
        endDate: parsedEndDate
      });
      const total = await ActivityLog.countDocuments(totalQuery.getQuery());

      res.json({
        activities: activityResponses,
        pagination: {
          total,
          limit: parsedLimit,
          skip: parsedSkip,
          hasMore: parsedSkip + parsedLimit < total
        },
        filters: {
          type: type || null,
          severity: severity || null,
          startDate: parsedStartDate?.toISOString() || null,
          endDate: parsedEndDate?.toISOString() || null
        }
      });
    } catch (error) {
      logger.error('Failed to get activity logs', { error, userId: req.user.id });
      res.status(500).json({
        error: 'Failed to get activity logs',
        code: 'ACTIVITY_ERROR'
      });
    }
  }

  public async getActivityStats(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { days = 30 } = req.query;
      const parsedDays = Math.min(parseInt(days as string) || 30, 365); // Max 1 year

      const stats = await ActivityLog.getActivityStats(req.user.id, parsedDays);

      // Get recent security events
      const securityEvents = await ActivityLog.findSecurityEvents(req.user.id, 'medium');
      const recentSecurityEvents = securityEvents.slice(0, 10);

      // Get activity by day for chart
      const startDate = new Date(Date.now() - parsedDays * 24 * 60 * 60 * 1000);
      const dailyActivity = await ActivityLog.aggregate([
        {
          $match: {
            userId: req.user.id as any,
            createdAt: { $gte: startDate }
          }
        },
        {
          $group: {
            _id: {
              $dateToString: {
                format: '%Y-%m-%d',
                date: '$createdAt'
              }
            },
            count: { $sum: 1 },
            severityBreakdown: {
              $push: '$severity'
            }
          }
        },
        {
          $sort: { _id: 1 }
        }
      ]);

      // Process daily activity for easier consumption
      const activityByDay = dailyActivity.reduce((acc, day) => {
        const severityCounts = day.severityBreakdown.reduce((counts: any, severity: string) => {
          counts[severity] = (counts[severity] || 0) + 1;
          return counts;
        }, {});

        acc[day._id] = {
          total: day.count,
          ...severityCounts
        };
        return acc;
      }, {} as Record<string, any>);

      // Calculate summary stats
      const totalActivities = stats.reduce((sum, stat) => sum + stat.totalCount, 0);
      const criticalEvents = stats.filter(stat => 
        stat.severityBreakdown.some((s: any) => s.severity === 'critical')
      ).length;
      const highSeverityEvents = stats.filter(stat => 
        stat.severityBreakdown.some((s: any) => ['high', 'critical'].includes(s.severity))
      ).length;

      res.json({
        summary: {
          totalActivities,
          criticalEvents,
          highSeverityEvents,
          recentSecurityEventsCount: recentSecurityEvents.length,
          periodDays: parsedDays
        },
        activityByType: stats,
        activityByDay,
        recentSecurityEvents: recentSecurityEvents.map(event => ({
          id: event._id.toString(),
          type: event.type,
          description: event.description,
          severity: event.severity,
          timestamp: event.createdAt.toISOString()
        }))
      });
    } catch (error) {
      logger.error('Failed to get activity stats', { error, userId: req.user.id });
      res.status(500).json({
        error: 'Failed to get activity statistics',
        code: 'ACTIVITY_STATS_ERROR'
      });
    }
  }

  public async getSecurityEvents(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { severity = 'medium', limit = 20 } = req.query;
      const parsedLimit = Math.min(parseInt(limit as string) || 20, 50);

      const events = await ActivityLog.findSecurityEvents(
        req.user.id,
        severity as 'medium' | 'high' | 'critical'
      );

      const limitedEvents = events.slice(0, parsedLimit);

      const securityResponses: ActivityLogResponse[] = limitedEvents.map(event => ({
        id: event._id.toString(),
        type: event.type,
        description: event.description,
        details: event.details,
        ipAddress: event.ipAddress,
        userAgent: event.userAgent,
        location: event.location,
        severity: event.severity,
        timestamp: event.createdAt.toISOString()
      }));

      res.json({
        securityEvents: securityResponses,
        total: events.length,
        limit: parsedLimit,
        severity
      });
    } catch (error) {
      logger.error('Failed to get security events', { error, userId: req.user.id });
      res.status(500).json({
        error: 'Failed to get security events',
        code: 'SECURITY_EVENTS_ERROR'
      });
    }
  }

  public async getSuspiciousActivity(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { hours = 24 } = req.query;
      const parsedHours = Math.min(parseInt(hours as string) || 24, 168); // Max 7 days

      const suspiciousActivity = await ActivityLog.findSuspiciousActivity(req.user.id, parsedHours);

      const suspiciousResponses: ActivityLogResponse[] = suspiciousActivity.map(activity => ({
        id: activity._id.toString(),
        type: activity.type,
        description: activity.description,
        details: activity.details,
        ipAddress: activity.ipAddress,
        userAgent: activity.userAgent,
        location: activity.location,
        severity: activity.severity,
        timestamp: activity.createdAt.toISOString()
      }));

      // Analyze patterns
      const ipAddresses = [...new Set(suspiciousActivity.map(a => a.ipAddress))];
      const failedLogins = suspiciousActivity.filter(a => 
        a.type === 'login' && a.details?.success === false
      ).length;
      const securityEvents = suspiciousActivity.filter(a => a.type === 'security_event').length;

      res.json({
        suspiciousActivity: suspiciousResponses,
        analysis: {
          totalEvents: suspiciousActivity.length,
          uniqueIpAddresses: ipAddresses.length,
          failedLogins,
          securityEvents,
          periodHours: parsedHours
        },
        recommendations: this.generateSecurityRecommendations(suspiciousActivity)
      });
    } catch (error) {
      logger.error('Failed to get suspicious activity', { error, userId: req.user.id });
      res.status(500).json({
        error: 'Failed to get suspicious activity',
        code: 'SUSPICIOUS_ACTIVITY_ERROR'
      });
    }
  }

  private generateSecurityRecommendations(activities: any[]): string[] {
    const recommendations: string[] = [];

    const failedLogins = activities.filter(a => 
      a.type === 'login' && a.details?.success === false
    );
    
    if (failedLogins.length > 3) {
      recommendations.push('Consider enabling two-factor authentication for additional security');
    }

    const uniqueIPs = [...new Set(activities.map(a => a.ipAddress))];
    if (uniqueIPs.length > 3) {
      recommendations.push('Multiple IP addresses detected - review login locations');
    }

    const criticalEvents = activities.filter(a => a.severity === 'critical');
    if (criticalEvents.length > 0) {
      recommendations.push('Critical security events detected - review account security immediately');
    }

    const recentPasswordChanges = activities.filter(a => a.type === 'password_change');
    if (recentPasswordChanges.length === 0) {
      recommendations.push('Consider changing your password regularly for better security');
    }

    return recommendations;
  }
}
