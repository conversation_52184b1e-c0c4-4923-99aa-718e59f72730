import { Response } from 'express';
import { UserSession } from '../../models/users/UserSession';
import { ActivityLog } from '../../models/users/ActivityLog';
import { sessionService } from '../../services/auth/session';
import { AuthenticatedRequest } from '../../types/auth';
import { SessionResponse } from '../../types/database/user';
import { logger } from '../../utils/logger';
import { getClientIP } from '../../utils/request';

export class SessionsController {
  public async getSessions(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const sessions = await sessionService.getUserSessions(req.user.id, req.user.sessionId);
      
      const sessionResponses: SessionResponse[] = sessions.map(session => ({
        id: session.id,
        deviceName: session.deviceName,
        browser: session.browser,
        ipAddress: session.ipAddress,
        location: session.location,
        loginAt: session.loginAt.toISOString(),
        lastActiveAt: session.lastActiveAt.toISOString(),
        isCurrent: session.isCurrent
      }));

      res.json({
        sessions: sessionResponses,
        total: sessionResponses.length
      });
    } catch (error) {
      logger.error('Failed to get user sessions', { error, userId: req.user.id });
      res.status(500).json({
        error: 'Failed to get sessions',
        code: 'SESSIONS_ERROR'
      });
    }
  }

  public async revokeSession(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { id: sessionId } = req.params;
      
      if (!sessionId) {
        res.status(400).json({
          error: 'Session ID required',
          code: 'SESSION_ID_REQUIRED'
        });
        return;
      }

      // Prevent revoking current session through this endpoint
      if (sessionId === req.user.sessionId) {
        res.status(400).json({
          error: 'Cannot revoke current session',
          code: 'CANNOT_REVOKE_CURRENT_SESSION'
        });
        return;
      }

      // Get session details before revoking for logging
      const session = await UserSession.findOne({
        _id: sessionId,
        userId: req.user.id,
        isActive: true
      });

      if (!session) {
        res.status(404).json({
          error: 'Session not found',
          code: 'SESSION_NOT_FOUND'
        });
        return;
      }

      const success = await sessionService.revokeSession(sessionId, req.user.id);
      
      if (!success) {
        res.status(404).json({
          error: 'Session not found or already revoked',
          code: 'SESSION_NOT_FOUND'
        });
        return;
      }

      // Log session revocation
      await ActivityLog.logActivity({
        userId: req.user.id as any,
        type: 'security_event',
        description: 'Session revoked',
        details: {
          revokedSessionId: sessionId,
          deviceName: session.deviceName,
          browser: session.browser,
          ipAddress: session.ipAddress
        },
        ipAddress: getClientIP(req),
        userAgent: req.headers['user-agent'] || '',
        severity: 'medium'
      });

      logger.info('Session revoked', {
        userId: req.user.id,
        revokedSessionId: sessionId,
        deviceName: session.deviceName,
        ipAddress: getClientIP(req)
      });

      res.json({
        message: 'Session revoked successfully',
        sessionId
      });
    } catch (error) {
      logger.error('Failed to revoke session', { error, userId: req.user.id, sessionId: req.params.id });
      res.status(500).json({
        error: 'Failed to revoke session',
        code: 'SESSION_REVOKE_ERROR'
      });
    }
  }

  public async revokeAllSessions(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { keepCurrent = true } = req.body;
      
      const revokedCount = await sessionService.revokeAllSessions(
        req.user.id,
        keepCurrent ? req.user.sessionId : undefined
      );

      // Log mass session revocation
      await ActivityLog.logActivity({
        userId: req.user.id as any,
        type: 'security_event',
        description: keepCurrent ? 'All other sessions revoked' : 'All sessions revoked',
        details: {
          revokedCount,
          keepCurrent
        },
        ipAddress: getClientIP(req),
        userAgent: req.headers['user-agent'] || '',
        severity: 'high'
      });

      logger.info('Multiple sessions revoked', {
        userId: req.user.id,
        revokedCount,
        keepCurrent,
        ipAddress: getClientIP(req)
      });

      res.json({
        message: `${revokedCount} session${revokedCount !== 1 ? 's' : ''} revoked successfully`,
        revokedCount,
        keepCurrent
      });
    } catch (error) {
      logger.error('Failed to revoke all sessions', { error, userId: req.user.id });
      res.status(500).json({
        error: 'Failed to revoke sessions',
        code: 'SESSIONS_REVOKE_ERROR'
      });
    }
  }

  public async getSessionStats(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const activeCount = await sessionService.getActiveSessionsCount(req.user.id);
      
      // Get recent login activity
      const recentLogins = await ActivityLog.findRecentByType(req.user.id, 'login', 168); // Last 7 days
      
      // Group by day for chart data
      const loginsByDay: Record<string, number> = {};
      recentLogins.forEach(log => {
        const day = log.createdAt.toISOString().split('T')[0];
        loginsByDay[day] = (loginsByDay[day] || 0) + 1;
      });

      // Get unique devices/browsers
      const sessions = await UserSession.findActiveByUserId(req.user.id);
      const devices = [...new Set(sessions.map(s => s.deviceName))];
      const browsers = [...new Set(sessions.map(s => s.browser))];
      const locations = [...new Set(sessions.map(s => s.location).filter(Boolean))];

      res.json({
        activeSessionsCount: activeCount,
        recentLoginsCount: recentLogins.length,
        loginsByDay,
        uniqueDevices: devices.length,
        uniqueBrowsers: browsers.length,
        uniqueLocations: locations.length,
        devices,
        browsers,
        locations
      });
    } catch (error) {
      logger.error('Failed to get session stats', { error, userId: req.user.id });
      res.status(500).json({
        error: 'Failed to get session statistics',
        code: 'SESSION_STATS_ERROR'
      });
    }
  }
}
