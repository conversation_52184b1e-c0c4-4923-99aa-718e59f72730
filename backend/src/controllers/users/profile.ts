import { Response } from 'express';
import { User } from '../../models/users/User';
import { UserSession } from '../../models/users/UserSession';
import { ApiKey } from '../../models/users/ApiKey';
import { ActivityLog } from '../../models/users/ActivityLog';
import { AuthenticatedRequest } from '../../types/auth';
import { UserResponse, SessionResponse, ApiKeyResponse, ActivityLogResponse } from '../../types/database/user';
import { logger } from '../../utils/logger';
import { getClientIP } from '../../utils/request';
import { validateProfileUpdate, validateSecurityUpdate, validatePreferencesUpdate } from '../../validators/users';

export class UserProfileController {
  public async getProfile(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const user = await User.findById(req.user.id);
      if (!user) {
        res.status(404).json({
          error: 'User not found',
          code: 'USER_NOT_FOUND'
        });
        return;
      }

      const userResponse: UserResponse = {
        id: user._id.toString(),
        profile: {
          ...user.profile,
          createdAt: user.profile.createdAt.toISOString(),
          lastLoginAt: user.profile.lastLoginAt?.toISOString()
        },
        security: {
          ...user.security,
          passwordLastChanged: user.security.passwordLastChanged.toISOString(),
          // Remove sensitive fields
          passwordHash: undefined as any,
          twoFactorSecret: undefined as any
        },
        billing: user.billing,
        notifications: user.notifications,
        preferences: user.preferences,
        role: user.role,
        status: user.status,
        emailVerified: user.emailVerified
      };

      res.json(userResponse);
    } catch (error) {
      logger.error('Failed to get user profile', { error, userId: req.user.id });
      res.status(500).json({
        error: 'Failed to get profile',
        code: 'PROFILE_ERROR'
      });
    }
  }

  public async updateProfile(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const validation = validateProfileUpdate(req.body);
      if (!validation.isValid) {
        res.status(400).json({
          error: 'Validation failed',
          code: 'VALIDATION_ERROR',
          details: validation.errors
        });
        return;
      }

      const user = await User.findById(req.user.id);
      if (!user) {
        res.status(404).json({
          error: 'User not found',
          code: 'USER_NOT_FOUND'
        });
        return;
      }

      // Track changes for activity log
      const changes: Record<string, any> = {};
      const updates = req.body;

      // Update profile fields
      if (updates.firstName && updates.firstName !== user.profile.firstName) {
        changes.firstName = { from: user.profile.firstName, to: updates.firstName };
        user.profile.firstName = updates.firstName;
      }

      if (updates.lastName && updates.lastName !== user.profile.lastName) {
        changes.lastName = { from: user.profile.lastName, to: updates.lastName };
        user.profile.lastName = updates.lastName;
      }

      if (updates.phone !== user.profile.phone) {
        changes.phone = { from: user.profile.phone, to: updates.phone };
        user.profile.phone = updates.phone;
      }

      if (updates.avatar !== user.profile.avatar) {
        changes.avatar = { from: !!user.profile.avatar, to: !!updates.avatar };
        user.profile.avatar = updates.avatar;
      }

      // Update company information
      if (updates.company) {
        if (!user.profile.company) user.profile.company = {} as any;
        
        Object.keys(updates.company).forEach(key => {
          if (updates.company[key] !== user.profile.company![key as keyof typeof user.profile.company]) {
            changes[`company.${key}`] = { 
              from: user.profile.company![key as keyof typeof user.profile.company], 
              to: updates.company[key] 
            };
            (user.profile.company as any)[key] = updates.company[key];
          }
        });
      }

      // Update address
      if (updates.address) {
        Object.keys(updates.address).forEach(key => {
          if (updates.address[key] !== user.profile.address[key as keyof typeof user.profile.address]) {
            changes[`address.${key}`] = { 
              from: user.profile.address[key as keyof typeof user.profile.address], 
              to: updates.address[key] 
            };
            (user.profile.address as any)[key] = updates.address[key];
          }
        });
      }

      // Update localization
      if (updates.timezone && updates.timezone !== user.profile.timezone) {
        changes.timezone = { from: user.profile.timezone, to: updates.timezone };
        user.profile.timezone = updates.timezone;
      }

      if (updates.language && updates.language !== user.profile.language) {
        changes.language = { from: user.profile.language, to: updates.language };
        user.profile.language = updates.language;
      }

      await user.save();

      // Log activity if there were changes
      if (Object.keys(changes).length > 0) {
        await ActivityLog.logActivity({
          userId: user._id,
          type: 'profile_update',
          description: 'Profile information updated',
          details: { changes },
          ipAddress: getClientIP(req),
          userAgent: req.headers['user-agent'] || '',
          severity: 'low'
        });
      }

      logger.info('User profile updated', {
        userId: req.user.id,
        changes: Object.keys(changes),
        ipAddress: getClientIP(req)
      });

      res.json({
        message: 'Profile updated successfully',
        changes: Object.keys(changes)
      });
    } catch (error) {
      logger.error('Failed to update user profile', { error, userId: req.user.id });
      res.status(500).json({
        error: 'Failed to update profile',
        code: 'PROFILE_UPDATE_ERROR'
      });
    }
  }

  public async updateSecurity(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const validation = validateSecurityUpdate(req.body);
      if (!validation.isValid) {
        res.status(400).json({
          error: 'Validation failed',
          code: 'VALIDATION_ERROR',
          details: validation.errors
        });
        return;
      }

      const user = await User.findById(req.user.id);
      if (!user) {
        res.status(404).json({
          error: 'User not found',
          code: 'USER_NOT_FOUND'
        });
        return;
      }

      const updates = req.body;
      const changes: Record<string, any> = {};

      // Update notification preferences
      if (updates.loginNotifications !== undefined && updates.loginNotifications !== user.security.loginNotifications) {
        changes.loginNotifications = { from: user.security.loginNotifications, to: updates.loginNotifications };
        user.security.loginNotifications = updates.loginNotifications;
      }

      if (updates.suspiciousActivityAlerts !== undefined && updates.suspiciousActivityAlerts !== user.security.suspiciousActivityAlerts) {
        changes.suspiciousActivityAlerts = { from: user.security.suspiciousActivityAlerts, to: updates.suspiciousActivityAlerts };
        user.security.suspiciousActivityAlerts = updates.suspiciousActivityAlerts;
      }

      // Handle 2FA toggle
      if (updates.twoFactorEnabled !== undefined && updates.twoFactorEnabled !== user.security.twoFactorEnabled) {
        changes.twoFactorEnabled = { from: user.security.twoFactorEnabled, to: updates.twoFactorEnabled };
        user.security.twoFactorEnabled = updates.twoFactorEnabled;
        
        if (!updates.twoFactorEnabled) {
          // Disable 2FA
          user.security.twoFactorMethod = undefined;
          user.security.twoFactorSecret = undefined;
          user.security.backupCodes = undefined;
        }
      }

      await user.save();

      // Log security changes
      if (Object.keys(changes).length > 0) {
        await ActivityLog.logActivity({
          userId: user._id,
          type: 'security_event',
          description: 'Security settings updated',
          details: { changes },
          ipAddress: getClientIP(req),
          userAgent: req.headers['user-agent'] || '',
          severity: 'medium'
        });
      }

      logger.info('User security settings updated', {
        userId: req.user.id,
        changes: Object.keys(changes),
        ipAddress: getClientIP(req)
      });

      res.json({
        message: 'Security settings updated successfully',
        changes: Object.keys(changes)
      });
    } catch (error) {
      logger.error('Failed to update security settings', { error, userId: req.user.id });
      res.status(500).json({
        error: 'Failed to update security settings',
        code: 'SECURITY_UPDATE_ERROR'
      });
    }
  }

  public async updatePreferences(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const validation = validatePreferencesUpdate(req.body);
      if (!validation.isValid) {
        res.status(400).json({
          error: 'Validation failed',
          code: 'VALIDATION_ERROR',
          details: validation.errors
        });
        return;
      }

      const user = await User.findById(req.user.id);
      if (!user) {
        res.status(404).json({
          error: 'User not found',
          code: 'USER_NOT_FOUND'
        });
        return;
      }

      const updates = req.body;
      const changes: Record<string, any> = {};

      // Update preferences
      Object.keys(updates).forEach(key => {
        if (key in user.preferences && JSON.stringify(updates[key]) !== JSON.stringify(user.preferences[key as keyof typeof user.preferences])) {
          changes[key] = { 
            from: user.preferences[key as keyof typeof user.preferences], 
            to: updates[key] 
          };
          (user.preferences as any)[key] = updates[key];
        }
      });

      await user.save();

      // Log preference changes
      if (Object.keys(changes).length > 0) {
        await ActivityLog.logActivity({
          userId: user._id,
          type: 'profile_update',
          description: 'Dashboard preferences updated',
          details: { changes },
          ipAddress: getClientIP(req),
          userAgent: req.headers['user-agent'] || '',
          severity: 'low'
        });
      }

      logger.info('User preferences updated', {
        userId: req.user.id,
        changes: Object.keys(changes),
        ipAddress: getClientIP(req)
      });

      res.json({
        message: 'Preferences updated successfully',
        changes: Object.keys(changes)
      });
    } catch (error) {
      logger.error('Failed to update preferences', { error, userId: req.user.id });
      res.status(500).json({
        error: 'Failed to update preferences',
        code: 'PREFERENCES_UPDATE_ERROR'
      });
    }
  }

  public async updateNotifications(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const user = await User.findById(req.user.id);
      if (!user) {
        res.status(404).json({
          error: 'User not found',
          code: 'USER_NOT_FOUND'
        });
        return;
      }

      const updates = req.body;
      const changes: Record<string, any> = {};

      // Update notification preferences
      ['email', 'sms', 'push', 'frequency'].forEach(category => {
        if (updates[category]) {
          Object.keys(updates[category]).forEach(key => {
            const currentValue = (user.notifications as any)[category][key];
            const newValue = updates[category][key];
            
            if (JSON.stringify(currentValue) !== JSON.stringify(newValue)) {
              changes[`${category}.${key}`] = { from: currentValue, to: newValue };
              (user.notifications as any)[category][key] = newValue;
            }
          });
        }
      });

      await user.save();

      // Log notification changes
      if (Object.keys(changes).length > 0) {
        await ActivityLog.logActivity({
          userId: user._id,
          type: 'profile_update',
          description: 'Notification preferences updated',
          details: { changes },
          ipAddress: getClientIP(req),
          userAgent: req.headers['user-agent'] || '',
          severity: 'low'
        });
      }

      logger.info('User notification preferences updated', {
        userId: req.user.id,
        changes: Object.keys(changes),
        ipAddress: getClientIP(req)
      });

      res.json({
        message: 'Notification preferences updated successfully',
        changes: Object.keys(changes)
      });
    } catch (error) {
      logger.error('Failed to update notification preferences', { error, userId: req.user.id });
      res.status(500).json({
        error: 'Failed to update notification preferences',
        code: 'NOTIFICATIONS_UPDATE_ERROR'
      });
    }
  }
}
