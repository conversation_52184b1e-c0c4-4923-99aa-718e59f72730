import crypto from 'crypto';
import { ObjectId } from 'mongodb';
import { UserSession } from '../../models/users/UserSession';
import { CreateSessionRequest, SessionInfo } from '../../types/auth';
import { jwtStrategy } from '../../auth/strategies/jwt';
import { logger } from '../../utils/logger';
import { getLocationFromIP } from '../../utils/geo';

export class SessionService {
  private static readonly MAX_SESSIONS_PER_USER = 10;
  private static readonly SESSION_CLEANUP_INTERVAL = 60 * 60 * 1000; // 1 hour

  constructor() {
    // Start periodic cleanup
    this.startPeriodicCleanup();
  }

  public async createSession(request: CreateSessionRequest): Promise<{
    session: SessionInfo;
    tokens: {
      accessToken: string;
      refreshToken: string;
      expiresIn: number;
    };
  }> {
    try {
      // Generate refresh token
      const refreshToken = this.generateRefreshToken();
      
      // Calculate expiry (7 days default, 30 days if remember me)
      const expiryDays = request.rememberMe ? 30 : 7;
      const expiresAt = new Date(Date.now() + expiryDays * 24 * 60 * 60 * 1000);
      
      // Get location from IP
      const location = await getLocationFromIP(request.ipAddress);
      
      // Create session document
      const sessionDoc = new UserSession({
        userId: request.userId,
        deviceName: request.deviceName || 'Unknown Device',
        browser: request.browser,
        ipAddress: request.ipAddress,
        location,
        userAgent: request.userAgent,
        refreshToken,
        loginAt: new Date(),
        lastActiveAt: new Date(),
        expiresAt,
        isActive: true
      });

      await sessionDoc.save();

      // Clean up old sessions if user has too many
      await this.cleanupOldSessions(request.userId);

      // Generate JWT tokens
      const user = await import('../../models/users/User').then(m => m.User.findById(request.userId));
      if (!user) {
        throw new Error('User not found');
      }

      const tokens = jwtStrategy.generateTokenPair({
        userId: request.userId.toString(),
        email: user.profile.email,
        role: user.role,
        sessionId: sessionDoc._id.toString()
      });

      const sessionInfo: SessionInfo = {
        id: sessionDoc._id.toString(),
        userId: request.userId.toString(),
        deviceName: sessionDoc.deviceName,
        browser: sessionDoc.browser,
        ipAddress: sessionDoc.ipAddress,
        location: sessionDoc.location,
        loginAt: sessionDoc.loginAt,
        lastActiveAt: sessionDoc.lastActiveAt,
        expiresAt: sessionDoc.expiresAt,
        isCurrent: true
      };

      logger.info('Session created successfully', {
        userId: request.userId.toString(),
        sessionId: sessionDoc._id.toString(),
        deviceName: request.deviceName,
        ipAddress: request.ipAddress
      });

      return { session: sessionInfo, tokens };
    } catch (error) {
      logger.error('Failed to create session', { error, userId: request.userId.toString() });
      throw error;
    }
  }

  public async refreshSession(refreshToken: string): Promise<{
    accessToken: string;
    expiresIn: number;
  } | null> {
    try {
      // Find session by refresh token
      const session = await UserSession.findByRefreshToken(refreshToken);
      if (!session || !session.isActive || session.isExpired()) {
        return null;
      }

      // Get user details
      const user = await import('../../models/users/User').then(m => m.User.findById(session.userId));
      if (!user || user.status !== 'active') {
        await session.deactivate();
        return null;
      }

      // Update session activity
      await session.updateActivity();

      // Generate new access token
      const accessToken = jwtStrategy.generateAccessToken({
        userId: session.userId.toString(),
        email: user.profile.email,
        role: user.role,
        sessionId: session._id.toString()
      });

      const expiresIn = 15 * 60; // 15 minutes in seconds

      logger.debug('Session refreshed', {
        userId: session.userId.toString(),
        sessionId: session._id.toString()
      });

      return { accessToken, expiresIn };
    } catch (error) {
      logger.error('Failed to refresh session', { error });
      return null;
    }
  }

  public async validateSession(sessionId: string): Promise<SessionInfo | null> {
    try {
      const session = await UserSession.findById(sessionId);
      if (!session || !session.isActive || session.isExpired()) {
        return null;
      }

      // Update last active time
      await session.updateActivity();

      return {
        id: session._id.toString(),
        userId: session.userId.toString(),
        deviceName: session.deviceName,
        browser: session.browser,
        ipAddress: session.ipAddress,
        location: session.location,
        loginAt: session.loginAt,
        lastActiveAt: session.lastActiveAt,
        expiresAt: session.expiresAt,
        isCurrent: true
      };
    } catch (error) {
      logger.error('Failed to validate session', { error, sessionId });
      return null;
    }
  }

  public async getUserSessions(userId: string, currentSessionId?: string): Promise<SessionInfo[]> {
    try {
      const sessions = await UserSession.findActiveByUserId(userId);
      
      return sessions.map(session => ({
        id: session._id.toString(),
        userId: session.userId.toString(),
        deviceName: session.deviceName,
        browser: session.browser,
        ipAddress: session.ipAddress,
        location: session.location,
        loginAt: session.loginAt,
        lastActiveAt: session.lastActiveAt,
        expiresAt: session.expiresAt,
        isCurrent: currentSessionId === session._id.toString()
      }));
    } catch (error) {
      logger.error('Failed to get user sessions', { error, userId });
      return [];
    }
  }

  public async revokeSession(sessionId: string, userId?: string): Promise<boolean> {
    try {
      const query: any = { _id: new ObjectId(sessionId) };
      if (userId) {
        query.userId = new ObjectId(userId);
      }

      const session = await UserSession.findOne(query);
      if (!session) {
        return false;
      }

      await session.deactivate();

      logger.info('Session revoked', {
        sessionId,
        userId: session.userId.toString()
      });

      return true;
    } catch (error) {
      logger.error('Failed to revoke session', { error, sessionId });
      return false;
    }
  }

  public async revokeAllSessions(userId: string, exceptSessionId?: string): Promise<number> {
    try {
      const result = await UserSession.deactivateAllForUser(userId, exceptSessionId);
      
      logger.info('All sessions revoked for user', {
        userId,
        exceptSessionId,
        revokedCount: result.modifiedCount
      });

      return result.modifiedCount || 0;
    } catch (error) {
      logger.error('Failed to revoke all sessions', { error, userId });
      return 0;
    }
  }

  public async getActiveSessionsCount(userId: string): Promise<number> {
    try {
      return await UserSession.getActiveSessionsCount(userId);
    } catch (error) {
      logger.error('Failed to get active sessions count', { error, userId });
      return 0;
    }
  }

  private async cleanupOldSessions(userId: ObjectId): Promise<void> {
    try {
      const sessionCount = await UserSession.getActiveSessionsCount(userId.toString());
      
      if (sessionCount > SessionService.MAX_SESSIONS_PER_USER) {
        // Get oldest sessions to remove
        const oldSessions = await UserSession.find({
          userId,
          isActive: true
        })
        .sort({ lastActiveAt: 1 })
        .limit(sessionCount - SessionService.MAX_SESSIONS_PER_USER);

        // Deactivate old sessions
        for (const session of oldSessions) {
          await session.deactivate();
        }

        logger.info('Cleaned up old sessions', {
          userId: userId.toString(),
          removedCount: oldSessions.length
        });
      }
    } catch (error) {
      logger.error('Failed to cleanup old sessions', { error, userId: userId.toString() });
    }
  }

  private generateRefreshToken(): string {
    return crypto.randomBytes(64).toString('hex');
  }

  private startPeriodicCleanup(): void {
    setInterval(async () => {
      try {
        const result = await UserSession.cleanupExpired();
        if (result.deletedCount && result.deletedCount > 0) {
          logger.info('Cleaned up expired sessions', { count: result.deletedCount });
        }
      } catch (error) {
        logger.error('Failed to cleanup expired sessions', { error });
      }
    }, SessionService.SESSION_CLEANUP_INTERVAL);
  }
}

export const sessionService = new SessionService();
