import jwt from 'jsonwebtoken';
import crypto from 'crypto';
import { JwtPayload, RefreshTokenPayload } from '../../types/auth';
import { logger } from '../../utils/logger';

interface TokenConfig {
  accessTokenSecret: string;
  refreshTokenSecret: string;
  accessTokenExpiry: string;
  refreshTokenExpiry: string;
}

class JWTStrategy {
  private config: TokenConfig;

  constructor() {
    this.config = {
      accessTokenSecret: process.env.JWT_ACCESS_SECRET || this.generateSecret(),
      refreshTokenSecret: process.env.JWT_REFRESH_SECRET || this.generateSecret(),
      accessTokenExpiry: process.env.JWT_ACCESS_EXPIRY || '15m',
      refreshTokenExpiry: process.env.JWT_REFRESH_EXPIRY || '7d'
    };

    if (!process.env.JWT_ACCESS_SECRET || !process.env.JWT_REFRESH_SECRET) {
      logger.warn('JWT secrets not provided in environment variables. Using generated secrets.');
    }
  }

  private generateSecret(): string {
    return crypto.randomBytes(64).toString('hex');
  }

  public generateAccessToken(payload: {
    userId: string;
    email: string;
    role: 'user' | 'admin';
    sessionId: string;
  }): string {
    const tokenPayload: Omit<JwtPayload, 'iat' | 'exp'> = {
      userId: payload.userId,
      email: payload.email,
      role: payload.role,
      sessionId: payload.sessionId
    };

    return jwt.sign(tokenPayload, this.config.accessTokenSecret, {
      expiresIn: this.config.accessTokenExpiry,
      issuer: 'luminexclient',
      audience: 'luminexclient-users'
    });
  }

  public generateRefreshToken(payload: {
    userId: string;
    sessionId: string;
    tokenVersion?: number;
  }): string {
    const tokenPayload: Omit<RefreshTokenPayload, 'iat' | 'exp'> = {
      userId: payload.userId,
      sessionId: payload.sessionId,
      tokenVersion: payload.tokenVersion || 1
    };

    return jwt.sign(tokenPayload, this.config.refreshTokenSecret, {
      expiresIn: this.config.refreshTokenExpiry,
      issuer: 'luminexclient',
      audience: 'luminexclient-refresh'
    });
  }

  public verifyAccessToken(token: string): JwtPayload | null {
    try {
      const decoded = jwt.verify(token, this.config.accessTokenSecret, {
        issuer: 'luminexclient',
        audience: 'luminexclient-users'
      }) as JwtPayload;

      return decoded;
    } catch (error) {
      if (error instanceof jwt.TokenExpiredError) {
        logger.debug('Access token expired');
      } else if (error instanceof jwt.JsonWebTokenError) {
        logger.debug('Invalid access token', { error: error.message });
      } else {
        logger.error('Access token verification failed', { error });
      }
      return null;
    }
  }

  public verifyRefreshToken(token: string): RefreshTokenPayload | null {
    try {
      const decoded = jwt.verify(token, this.config.refreshTokenSecret, {
        issuer: 'luminexclient',
        audience: 'luminexclient-refresh'
      }) as RefreshTokenPayload;

      return decoded;
    } catch (error) {
      if (error instanceof jwt.TokenExpiredError) {
        logger.debug('Refresh token expired');
      } else if (error instanceof jwt.JsonWebTokenError) {
        logger.debug('Invalid refresh token', { error: error.message });
      } else {
        logger.error('Refresh token verification failed', { error });
      }
      return null;
    }
  }

  public getTokenExpiry(token: string): Date | null {
    try {
      const decoded = jwt.decode(token) as any;
      if (decoded && decoded.exp) {
        return new Date(decoded.exp * 1000);
      }
      return null;
    } catch (error) {
      logger.error('Failed to decode token expiry', { error });
      return null;
    }
  }

  public getTokenPayload(token: string): any {
    try {
      return jwt.decode(token);
    } catch (error) {
      logger.error('Failed to decode token payload', { error });
      return null;
    }
  }

  public generateTokenPair(payload: {
    userId: string;
    email: string;
    role: 'user' | 'admin';
    sessionId: string;
    tokenVersion?: number;
  }): {
    accessToken: string;
    refreshToken: string;
    expiresIn: number;
  } {
    const accessToken = this.generateAccessToken(payload);
    const refreshToken = this.generateRefreshToken(payload);
    
    // Calculate expiry in seconds
    const expiresIn = this.parseExpiry(this.config.accessTokenExpiry);

    return {
      accessToken,
      refreshToken,
      expiresIn
    };
  }

  private parseExpiry(expiry: string): number {
    // Convert expiry string to seconds
    const match = expiry.match(/^(\d+)([smhd])$/);
    if (!match) return 900; // Default 15 minutes

    const value = parseInt(match[1]);
    const unit = match[2];

    switch (unit) {
      case 's': return value;
      case 'm': return value * 60;
      case 'h': return value * 60 * 60;
      case 'd': return value * 24 * 60 * 60;
      default: return 900;
    }
  }

  public isTokenExpired(token: string): boolean {
    const expiry = this.getTokenExpiry(token);
    return expiry ? expiry < new Date() : true;
  }

  public getTimeUntilExpiry(token: string): number {
    const expiry = this.getTokenExpiry(token);
    if (!expiry) return 0;
    
    const now = new Date();
    return Math.max(0, expiry.getTime() - now.getTime());
  }

  public extractTokenFromHeader(authHeader: string | undefined): string | null {
    if (!authHeader) return null;
    
    const parts = authHeader.split(' ');
    if (parts.length !== 2 || parts[0] !== 'Bearer') {
      return null;
    }
    
    return parts[1];
  }

  public createApiToken(payload: {
    userId: string;
    keyId: string;
    permissions: string[];
  }): string {
    return jwt.sign(
      {
        userId: payload.userId,
        keyId: payload.keyId,
        permissions: payload.permissions,
        type: 'api'
      },
      this.config.accessTokenSecret,
      {
        expiresIn: '1h', // API tokens have shorter expiry
        issuer: 'luminexclient',
        audience: 'luminexclient-api'
      }
    );
  }

  public verifyApiToken(token: string): {
    userId: string;
    keyId: string;
    permissions: string[];
  } | null {
    try {
      const decoded = jwt.verify(token, this.config.accessTokenSecret, {
        issuer: 'luminexclient',
        audience: 'luminexclient-api'
      }) as any;

      if (decoded.type !== 'api') {
        return null;
      }

      return {
        userId: decoded.userId,
        keyId: decoded.keyId,
        permissions: decoded.permissions || []
      };
    } catch (error) {
      logger.debug('API token verification failed', { error: error.message });
      return null;
    }
  }
}

export const jwtStrategy = new JWTStrategy();
