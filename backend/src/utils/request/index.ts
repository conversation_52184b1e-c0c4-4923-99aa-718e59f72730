import { Request } from 'express';

export function getClientIP(req: Request): string {
  // Check various headers for the real IP address
  const forwarded = req.headers['x-forwarded-for'];
  const realIP = req.headers['x-real-ip'];
  const cfConnectingIP = req.headers['cf-connecting-ip']; // Cloudflare
  
  if (forwarded) {
    // x-forwarded-for can contain multiple IPs, take the first one
    const ips = (forwarded as string).split(',');
    return ips[0].trim();
  }
  
  if (realIP) {
    return realIP as string;
  }
  
  if (cfConnectingIP) {
    return cfConnectingIP as string;
  }
  
  // Fallback to connection remote address
  return req.connection.remoteAddress || 
         req.socket.remoteAddress || 
         (req.connection as any).socket?.remoteAddress || 
         '127.0.0.1';
}

export interface ParsedUserAgent {
  browser: string;
  version: string;
  os: string;
  device: string;
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
}

export function parseUserAgent(userAgent: string): ParsedUserAgent {
  if (!userAgent) {
    return {
      browser: 'Unknown',
      version: '',
      os: 'Unknown',
      device: 'Unknown Device',
      isMobile: false,
      isTablet: false,
      isDesktop: true
    };
  }

  // Simple user agent parsing - in production, consider using a library like 'ua-parser-js'
  const ua = userAgent.toLowerCase();
  
  // Detect browser
  let browser = 'Unknown';
  let version = '';
  
  if (ua.includes('chrome') && !ua.includes('edg')) {
    browser = 'Chrome';
    const match = ua.match(/chrome\/([0-9.]+)/);
    version = match ? match[1] : '';
  } else if (ua.includes('firefox')) {
    browser = 'Firefox';
    const match = ua.match(/firefox\/([0-9.]+)/);
    version = match ? match[1] : '';
  } else if (ua.includes('safari') && !ua.includes('chrome')) {
    browser = 'Safari';
    const match = ua.match(/version\/([0-9.]+)/);
    version = match ? match[1] : '';
  } else if (ua.includes('edg')) {
    browser = 'Edge';
    const match = ua.match(/edg\/([0-9.]+)/);
    version = match ? match[1] : '';
  } else if (ua.includes('opera') || ua.includes('opr')) {
    browser = 'Opera';
    const match = ua.match(/(?:opera|opr)\/([0-9.]+)/);
    version = match ? match[1] : '';
  }

  // Detect OS
  let os = 'Unknown';
  if (ua.includes('windows')) {
    os = 'Windows';
  } else if (ua.includes('mac os x')) {
    os = 'macOS';
  } else if (ua.includes('linux')) {
    os = 'Linux';
  } else if (ua.includes('android')) {
    os = 'Android';
  } else if (ua.includes('iphone') || ua.includes('ipad')) {
    os = 'iOS';
  }

  // Detect device type
  const isMobile = ua.includes('mobile') || ua.includes('android') || ua.includes('iphone');
  const isTablet = ua.includes('tablet') || ua.includes('ipad');
  const isDesktop = !isMobile && !isTablet;

  let device = 'Unknown Device';
  if (isMobile) {
    if (ua.includes('iphone')) device = 'iPhone';
    else if (ua.includes('android')) device = 'Android Phone';
    else device = 'Mobile Device';
  } else if (isTablet) {
    if (ua.includes('ipad')) device = 'iPad';
    else device = 'Tablet';
  } else {
    if (ua.includes('mac')) device = 'Mac';
    else if (ua.includes('windows')) device = 'Windows PC';
    else if (ua.includes('linux')) device = 'Linux PC';
    else device = 'Desktop';
  }

  return {
    browser: version ? `${browser} ${version.split('.')[0]}` : browser,
    version,
    os,
    device,
    isMobile,
    isTablet,
    isDesktop
  };
}

export function sanitizeInput(input: string): string {
  if (!input) return '';
  
  // Remove potentially dangerous characters
  return input
    .replace(/[<>]/g, '') // Remove < and >
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, '') // Remove event handlers
    .trim();
}

export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

export function validatePassword(password: string): {
  isValid: boolean;
  errors: string[];
  strength: 'weak' | 'medium' | 'strong';
} {
  const errors: string[] = [];
  
  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long');
  }
  
  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }
  
  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }
  
  if (!/\d/.test(password)) {
    errors.push('Password must contain at least one number');
  }
  
  if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    errors.push('Password must contain at least one special character');
  }

  // Calculate strength
  let strength: 'weak' | 'medium' | 'strong' = 'weak';
  if (errors.length === 0) {
    if (password.length >= 12 && /[!@#$%^&*(),.?":{}|<>].*[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      strength = 'strong';
    } else {
      strength = 'medium';
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    strength
  };
}

export function generateRandomString(length: number = 32): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

export function isValidObjectId(id: string): boolean {
  return /^[0-9a-fA-F]{24}$/.test(id);
}
