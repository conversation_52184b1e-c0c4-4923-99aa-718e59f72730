// Simple geolocation utility
// In production, you might want to use a service like MaxMind GeoIP2 or ipapi.co

interface LocationInfo {
  city?: string;
  region?: string;
  country?: string;
  countryCode?: string;
  timezone?: string;
}

// Mock location data for common IP ranges (for development)
const mockLocationData: Record<string, LocationInfo> = {
  '127.0.0.1': {
    city: 'Local',
    region: 'Local',
    country: 'Local',
    countryCode: 'LC',
    timezone: 'UTC'
  },
  '192.168.': {
    city: 'Local Network',
    region: 'Local',
    country: 'Local',
    countryCode: 'LC',
    timezone: 'UTC'
  },
  '10.': {
    city: 'Private Network',
    region: 'Local',
    country: 'Local',
    countryCode: 'LC',
    timezone: 'UTC'
  }
};

export async function getLocationFromIP(ipAddress: string): Promise<string | undefined> {
  try {
    // Handle localhost and private IPs
    if (ipAddress === '127.0.0.1' || ipAddress === '::1') {
      return 'Local';
    }

    if (ipAddress.startsWith('192.168.') || ipAddress.startsWith('10.') || ipAddress.startsWith('172.')) {
      return 'Private Network';
    }

    // In development, return mock data
    if (process.env.NODE_ENV !== 'production') {
      return 'New York, NY, US'; // Mock location for development
    }

    // In production, you would integrate with a real geolocation service
    // Example with ipapi.co (free tier available):
    /*
    const response = await fetch(`https://ipapi.co/${ipAddress}/json/`);
    if (response.ok) {
      const data = await response.json();
      if (data.city && data.region && data.country_name) {
        return `${data.city}, ${data.region}, ${data.country_code}`;
      }
    }
    */

    // Fallback for production without geolocation service
    return undefined;
  } catch (error) {
    console.error('Failed to get location from IP:', error);
    return undefined;
  }
}

export async function getDetailedLocationFromIP(ipAddress: string): Promise<LocationInfo | null> {
  try {
    // Handle localhost and private IPs
    const mockKey = Object.keys(mockLocationData).find(key => ipAddress.startsWith(key));
    if (mockKey) {
      return mockLocationData[mockKey];
    }

    // In development, return mock data
    if (process.env.NODE_ENV !== 'production') {
      return {
        city: 'New York',
        region: 'New York',
        country: 'United States',
        countryCode: 'US',
        timezone: 'America/New_York'
      };
    }

    // In production, integrate with a real geolocation service
    // Example implementation:
    /*
    const response = await fetch(`https://ipapi.co/${ipAddress}/json/`);
    if (response.ok) {
      const data = await response.json();
      return {
        city: data.city,
        region: data.region,
        country: data.country_name,
        countryCode: data.country_code,
        timezone: data.timezone
      };
    }
    */

    return null;
  } catch (error) {
    console.error('Failed to get detailed location from IP:', error);
    return null;
  }
}

export function isPrivateIP(ipAddress: string): boolean {
  // Check for private IP ranges
  return (
    ipAddress === '127.0.0.1' ||
    ipAddress === '::1' ||
    ipAddress.startsWith('192.168.') ||
    ipAddress.startsWith('10.') ||
    ipAddress.startsWith('172.16.') ||
    ipAddress.startsWith('172.17.') ||
    ipAddress.startsWith('172.18.') ||
    ipAddress.startsWith('172.19.') ||
    ipAddress.startsWith('172.20.') ||
    ipAddress.startsWith('172.21.') ||
    ipAddress.startsWith('172.22.') ||
    ipAddress.startsWith('172.23.') ||
    ipAddress.startsWith('172.24.') ||
    ipAddress.startsWith('172.25.') ||
    ipAddress.startsWith('172.26.') ||
    ipAddress.startsWith('172.27.') ||
    ipAddress.startsWith('172.28.') ||
    ipAddress.startsWith('172.29.') ||
    ipAddress.startsWith('172.30.') ||
    ipAddress.startsWith('172.31.')
  );
}

export function formatLocation(location: LocationInfo): string {
  const parts: string[] = [];
  
  if (location.city) parts.push(location.city);
  if (location.region && location.region !== location.city) parts.push(location.region);
  if (location.countryCode) parts.push(location.countryCode);
  
  return parts.join(', ') || 'Unknown Location';
}
