interface LogLevel {
  DEBUG: 0;
  INFO: 1;
  WARN: 2;
  ERROR: 3;
}

const LOG_LEVELS: LogLevel = {
  DEBUG: 0,
  INFO: 1,
  WARN: 2,
  ERROR: 3
};

interface LogEntry {
  level: keyof LogLevel;
  message: string;
  timestamp: string;
  data?: any;
  stack?: string;
}

class Logger {
  private currentLevel: keyof LogLevel;

  constructor() {
    this.currentLevel = (process.env.LOG_LEVEL as keyof LogLevel) || 'INFO';
  }

  private shouldLog(level: keyof LogLevel): boolean {
    return LOG_LEVELS[level] >= LOG_LEVELS[this.currentLevel];
  }

  private formatMessage(level: keyof LogLevel, message: string, data?: any): LogEntry {
    const entry: LogEntry = {
      level,
      message,
      timestamp: new Date().toISOString()
    };

    if (data) {
      if (data instanceof Error) {
        entry.data = {
          name: data.name,
          message: data.message,
          stack: data.stack
        };
        entry.stack = data.stack;
      } else {
        entry.data = data;
      }
    }

    return entry;
  }

  private output(entry: LogEntry): void {
    const { level, message, timestamp, data, stack } = entry;
    
    // In production, you might want to send logs to a service like Winston, Bunyan, or external logging service
    if (process.env.NODE_ENV === 'production') {
      // Production logging - could integrate with external services
      console.log(JSON.stringify(entry));
    } else {
      // Development logging - more readable format
      const color = this.getColor(level);
      const reset = '\x1b[0m';
      
      let output = `${color}[${timestamp}] ${level}: ${message}${reset}`;
      
      if (data && Object.keys(data).length > 0) {
        output += `\n${JSON.stringify(data, null, 2)}`;
      }
      
      if (stack && level === 'ERROR') {
        output += `\n${stack}`;
      }
      
      console.log(output);
    }
  }

  private getColor(level: keyof LogLevel): string {
    const colors = {
      DEBUG: '\x1b[36m', // Cyan
      INFO: '\x1b[32m',  // Green
      WARN: '\x1b[33m',  // Yellow
      ERROR: '\x1b[31m'  // Red
    };
    return colors[level];
  }

  public debug(message: string, data?: any): void {
    if (this.shouldLog('DEBUG')) {
      const entry = this.formatMessage('DEBUG', message, data);
      this.output(entry);
    }
  }

  public info(message: string, data?: any): void {
    if (this.shouldLog('INFO')) {
      const entry = this.formatMessage('INFO', message, data);
      this.output(entry);
    }
  }

  public warn(message: string, data?: any): void {
    if (this.shouldLog('WARN')) {
      const entry = this.formatMessage('WARN', message, data);
      this.output(entry);
    }
  }

  public error(message: string, data?: any): void {
    if (this.shouldLog('ERROR')) {
      const entry = this.formatMessage('ERROR', message, data);
      this.output(entry);
    }
  }

  public setLevel(level: keyof LogLevel): void {
    this.currentLevel = level;
  }

  public getLevel(): keyof LogLevel {
    return this.currentLevel;
  }
}

export const logger = new Logger();
