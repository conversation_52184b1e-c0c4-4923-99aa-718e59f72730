import { ObjectId } from 'mongodb';

// JWT Payload interfaces
export interface JwtPayload {
  userId: string;
  email: string;
  role: "user" | "admin";
  sessionId: string;
  iat: number;
  exp: number;
}

export interface RefreshTokenPayload {
  userId: string;
  sessionId: string;
  tokenVersion: number;
  iat: number;
  exp: number;
}

// Authentication request/response types
export interface RegisterRequest {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  confirmPassword: string;
  company?: {
    name: string;
    website?: string;
    industry?: string;
    size?: string;
  };
  address: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  timezone?: string;
  language?: string;
  acceptTerms: boolean;
}

export interface LoginRequest {
  email: string;
  password: string;
  rememberMe?: boolean;
  deviceName?: string;
  twoFactorCode?: string;
}

export interface LoginResponse {
  user: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    role: "user" | "admin";
    emailVerified: boolean;
  };
  tokens: {
    accessToken: string;
    refreshToken: string;
    expiresIn: number;
  };
  session: {
    id: string;
    deviceName: string;
    expiresAt: string;
  };
  requiresTwoFactor?: boolean;
}

export interface RefreshTokenRequest {
  refreshToken: string;
}

export interface RefreshTokenResponse {
  accessToken: string;
  expiresIn: number;
}

export interface LogoutRequest {
  sessionId?: string;
  allSessions?: boolean;
}

export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

export interface ForgotPasswordRequest {
  email: string;
}

export interface ResetPasswordRequest {
  token: string;
  password: string;
  confirmPassword: string;
}

export interface VerifyEmailRequest {
  token: string;
}

export interface Enable2FARequest {
  method: "sms" | "app" | "email";
  phoneNumber?: string;
  backupEmail?: string;
}

export interface Verify2FARequest {
  code: string;
  backupCode?: string;
}

export interface Disable2FARequest {
  password: string;
  code?: string;
}

// Middleware types
export interface AuthenticatedRequest extends Request {
  user: {
    id: string;
    email: string;
    role: "user" | "admin";
    sessionId: string;
  };
}

export interface AuthContext {
  userId: ObjectId;
  email: string;
  role: "user" | "admin";
  sessionId: ObjectId;
  ipAddress: string;
  userAgent: string;
}

// Rate limiting types
export interface RateLimitConfig {
  windowMs: number;
  maxAttempts: number;
  blockDuration: number;
  skipSuccessfulRequests?: boolean;
}

export interface RateLimitInfo {
  totalHits: number;
  totalHitsInWindow: number;
  remainingPoints: number;
  msBeforeNext: number;
  isFirstInWindow: boolean;
}

// Session management types
export interface SessionInfo {
  id: string;
  userId: string;
  deviceName: string;
  browser: string;
  ipAddress: string;
  location?: string;
  loginAt: Date;
  lastActiveAt: Date;
  expiresAt: Date;
  isCurrent: boolean;
}

export interface CreateSessionRequest {
  userId: ObjectId;
  deviceName: string;
  browser: string;
  ipAddress: string;
  userAgent: string;
  location?: string;
  rememberMe?: boolean;
}

// API Key types
export interface CreateApiKeyRequest {
  name: string;
  permissions: string[];
  expiresAt?: string;
}

export interface ApiKeyInfo {
  id: string;
  name: string;
  keyPrefix: string;
  permissions: string[];
  lastUsedAt?: Date;
  expiresAt?: Date;
  isActive: boolean;
  createdAt: Date;
}

// Activity logging types
export interface LogActivityRequest {
  userId: ObjectId;
  type: "login" | "logout" | "password_change" | "profile_update" | "payment" | "service_action" | "api_access" | "security_event";
  description: string;
  details?: Record<string, any>;
  ipAddress: string;
  userAgent: string;
  location?: string;
  severity?: "low" | "medium" | "high" | "critical";
}

// Error types
export interface AuthError {
  code: string;
  message: string;
  details?: Record<string, any>;
}

export interface ValidationError {
  field: string;
  message: string;
  code: string;
}

// Security types
export interface SecurityEvent {
  type: "suspicious_login" | "multiple_failed_attempts" | "password_breach" | "unusual_activity";
  severity: "low" | "medium" | "high" | "critical";
  description: string;
  metadata: Record<string, any>;
  timestamp: Date;
}

export interface TwoFactorSetup {
  secret: string;
  qrCode: string;
  backupCodes: string[];
  method: "sms" | "app" | "email";
}

// Permission types
export const API_PERMISSIONS = {
  // User permissions
  'read:profile': 'Read user profile',
  'write:profile': 'Update user profile',
  'read:billing': 'Read billing information',
  'write:billing': 'Update billing information',
  
  // Service permissions
  'read:services': 'Read services',
  'write:services': 'Manage services',
  'read:domains': 'Read domains',
  'write:domains': 'Manage domains',
  
  // Support permissions
  'read:tickets': 'Read support tickets',
  'write:tickets': 'Create and update tickets',
  
  // Invoice permissions
  'read:invoices': 'Read invoices',
  'write:invoices': 'Create and update invoices',
  
  // Admin permissions
  'admin:users': 'Manage all users',
  'admin:system': 'System administration',
  'admin:billing': 'Billing administration',
} as const;

export type ApiPermission = keyof typeof API_PERMISSIONS;
