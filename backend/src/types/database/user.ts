import { Document, ObjectId } from 'mongodb';

// Base interfaces matching frontend types
export interface UserProfile {
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  avatar?: string;
  company?: {
    name: string;
    website?: string;
    industry?: string;
    size?: string;
  };
  address: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  timezone: string;
  language: string;
  createdAt: Date;
  lastLoginAt?: Date;
}

export interface SecuritySettings {
  passwordHash: string;
  passwordLastChanged: Date;
  twoFactorEnabled: boolean;
  twoFactorMethod?: "sms" | "app" | "email";
  twoFactorSecret?: string;
  backupCodes?: string[];
  securityQuestions?: SecurityQuestion[];
  loginNotifications: boolean;
  suspiciousActivityAlerts: boolean;
  failedLoginAttempts: number;
  lockedUntil?: Date;
}

export interface SecurityQuestion {
  id: string;
  question: string;
  answerHash: string;
}

export interface BillingSettings {
  defaultPaymentMethod?: ObjectId;
  billingAddress: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  taxInformation: {
    taxId?: string;
    vatNumber?: string;
    taxExempt: boolean;
    taxExemptReason?: string;
  };
  invoicePreferences: {
    emailInvoices: boolean;
    paperInvoices: boolean;
    invoiceEmail?: string;
    autoPayEnabled: boolean;
    paymentTerms: number;
  };
  subscriptionPreferences: {
    autoRenew: boolean;
    renewalReminders: boolean;
    upgradeNotifications: boolean;
  };
}

export interface NotificationPreferences {
  email: {
    serviceUpdates: boolean;
    maintenanceAlerts: boolean;
    billingReminders: boolean;
    invoiceNotifications: boolean;
    ticketUpdates: boolean;
    securityAlerts: boolean;
    marketingEmails: boolean;
    weeklyReports: boolean;
  };
  sms: {
    enabled: boolean;
    phoneNumber?: string;
    criticalAlerts: boolean;
    serviceDowntime: boolean;
    billingIssues: boolean;
    securityAlerts: boolean;
  };
  push: {
    enabled: boolean;
    serviceUpdates: boolean;
    ticketResponses: boolean;
    billingReminders: boolean;
  };
  frequency: {
    digest: "immediate" | "hourly" | "daily" | "weekly";
    quietHours: {
      enabled: boolean;
      start: string;
      end: string;
    };
  };
}

export interface DashboardPreferences {
  theme: "dark" | "light" | "auto";
  compactMode: boolean;
  showWelcomeMessage: boolean;
  defaultDashboardView: "overview" | "services" | "tickets" | "invoices";
  widgetPreferences: {
    showStats: boolean;
    showRecentActivity: boolean;
    showQuickActions: boolean;
    showSystemStatus: boolean;
  };
  tablePreferences: {
    itemsPerPage: number;
    defaultSort: string;
    compactTables: boolean;
  };
}

// Database document interfaces
export interface UserDocument extends Document {
  _id: ObjectId;
  profile: UserProfile;
  security: SecuritySettings;
  billing: BillingSettings;
  notifications: NotificationPreferences;
  preferences: DashboardPreferences;
  role: "user" | "admin";
  status: "active" | "suspended" | "pending";
  emailVerified: boolean;
  emailVerificationToken?: string;
  passwordResetToken?: string;
  passwordResetExpires?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface UserSessionDocument extends Document {
  _id: ObjectId;
  userId: ObjectId;
  deviceName: string;
  browser: string;
  ipAddress: string;
  location?: string;
  userAgent: string;
  refreshToken: string;
  refreshTokenHash: string;
  loginAt: Date;
  lastActiveAt: Date;
  expiresAt: Date;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface ApiKeyDocument extends Document {
  _id: ObjectId;
  userId: ObjectId;
  name: string;
  keyHash: string;
  keyPrefix: string; // First 8 chars for display
  permissions: string[];
  lastUsedAt?: Date;
  expiresAt?: Date;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface ActivityLogDocument extends Document {
  _id: ObjectId;
  userId: ObjectId;
  type: "login" | "logout" | "password_change" | "profile_update" | "payment" | "service_action" | "api_access" | "security_event";
  description: string;
  details?: Record<string, any>;
  ipAddress: string;
  userAgent: string;
  location?: string;
  severity: "low" | "medium" | "high" | "critical";
  createdAt: Date;
}

export interface PaymentMethodDocument extends Document {
  _id: ObjectId;
  userId: ObjectId;
  type: "credit-card" | "paypal" | "bank-account";
  isDefault: boolean;
  details: {
    last4?: string;
    brand?: string;
    expiryMonth?: number;
    expiryYear?: number;
    email?: string;
    bankName?: string;
    accountType?: string;
  };
  gatewayId?: string; // External payment gateway ID
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// API response types
export interface UserResponse {
  id: string;
  profile: Omit<UserProfile, 'createdAt' | 'lastLoginAt'> & {
    createdAt: string;
    lastLoginAt?: string;
  };
  security: Omit<SecuritySettings, 'passwordHash' | 'passwordLastChanged' | 'twoFactorSecret'> & {
    passwordLastChanged: string;
  };
  billing: BillingSettings;
  notifications: NotificationPreferences;
  preferences: DashboardPreferences;
  role: "user" | "admin";
  status: "active" | "suspended" | "pending";
  emailVerified: boolean;
}

export interface SessionResponse {
  id: string;
  deviceName: string;
  browser: string;
  ipAddress: string;
  location?: string;
  loginAt: string;
  lastActiveAt: string;
  isCurrent: boolean;
}

export interface ApiKeyResponse {
  id: string;
  name: string;
  key?: string; // Only returned on creation
  keyPrefix: string;
  permissions: string[];
  lastUsedAt?: string;
  expiresAt?: string;
  isActive: boolean;
  createdAt: string;
}

export interface ActivityLogResponse {
  id: string;
  type: string;
  description: string;
  details?: Record<string, any>;
  ipAddress: string;
  userAgent: string;
  location?: string;
  severity: "low" | "medium" | "high" | "critical";
  timestamp: string;
}
