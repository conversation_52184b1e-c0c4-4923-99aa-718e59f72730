import mongoose from 'mongoose';
import { logger } from '../../utils/logger';

interface MongoConfig {
  uri: string;
  options: mongoose.ConnectOptions;
}

class MongoDBConnection {
  private static instance: MongoDBConnection;
  private isConnected: boolean = false;
  private connectionPromise: Promise<typeof mongoose> | null = null;

  private constructor() {}

  public static getInstance(): MongoDBConnection {
    if (!MongoDBConnection.instance) {
      MongoDBConnection.instance = new MongoDBConnection();
    }
    return MongoDBConnection.instance;
  }

  private getConfig(): MongoConfig {
    const uri = process.env.MONGODB_URI;
    
    const options: mongoose.ConnectOptions = {
      maxPoolSize: 10, // Maintain up to 10 socket connections
      serverSelectionTimeoutMS: 5000, // Keep trying to send operations for 5 seconds
      socketTimeoutMS: 45000, // Close sockets after 45 seconds of inactivity
      bufferCommands: false, // Disable mongoose buffering
      bufferMaxEntries: 0, // Disable mongoose buffering
      retryWrites: true,
      retryReads: true,
      compressors: ['zlib'],
    };

    return { uri, options };
  }

  public async connect(): Promise<typeof mongoose> {
    if (this.isConnected && mongoose.connection.readyState === 1) {
      return mongoose;
    }

    if (this.connectionPromise) {
      return this.connectionPromise;
    }

    const config = this.getConfig();
    
    this.connectionPromise = mongoose.connect(config.uri, config.options);

    try {
      const connection = await this.connectionPromise;
      this.isConnected = true;
      
      logger.info('MongoDB connected successfully', {
        host: mongoose.connection.host,
        port: mongoose.connection.port,
        database: mongoose.connection.name
      });

      this.setupEventListeners();
      return connection;
    } catch (error) {
      this.connectionPromise = null;
      logger.error('MongoDB connection failed', { error });
      throw error;
    }
  }

  public async disconnect(): Promise<void> {
    if (this.isConnected) {
      await mongoose.disconnect();
      this.isConnected = false;
      this.connectionPromise = null;
      logger.info('MongoDB disconnected');
    }
  }

  public isHealthy(): boolean {
    return this.isConnected && mongoose.connection.readyState === 1;
  }

  public getConnectionState(): string {
    const states = {
      0: 'disconnected',
      1: 'connected',
      2: 'connecting',
      3: 'disconnecting',
      99: 'uninitialized'
    };
    return states[mongoose.connection.readyState as keyof typeof states] || 'unknown';
  }

  private setupEventListeners(): void {
    mongoose.connection.on('connected', () => {
      logger.info('MongoDB connection established');
    });

    mongoose.connection.on('error', (error) => {
      logger.error('MongoDB connection error', { error });
    });

    mongoose.connection.on('disconnected', () => {
      logger.warn('MongoDB disconnected');
      this.isConnected = false;
    });

    mongoose.connection.on('reconnected', () => {
      logger.info('MongoDB reconnected');
      this.isConnected = true;
    });

    // Handle application termination
    process.on('SIGINT', async () => {
      await this.disconnect();
      process.exit(0);
    });

    process.on('SIGTERM', async () => {
      await this.disconnect();
      process.exit(0);
    });
  }

  public async createIndexes(): Promise<void> {
    try {
      logger.info('Creating database indexes...');
      
      // Import models to ensure indexes are created
      await import('../../models/users/User');
      await import('../../models/users/UserSession');
      await import('../../models/users/ApiKey');
      await import('../../models/users/ActivityLog');
      await import('../../models/billing/PaymentMethod');
      
      // Ensure indexes are created
      await mongoose.connection.db.admin().command({ listCollections: 1 });
      
      logger.info('Database indexes created successfully');
    } catch (error) {
      logger.error('Failed to create database indexes', { error });
      throw error;
    }
  }

  public async healthCheck(): Promise<{
    status: 'healthy' | 'unhealthy';
    details: {
      state: string;
      host?: string;
      port?: number;
      database?: string;
      uptime?: number;
    };
  }> {
    try {
      const isHealthy = this.isHealthy();
      const state = this.getConnectionState();
      
      if (!isHealthy) {
        return {
          status: 'unhealthy',
          details: { state }
        };
      }

      // Test database operation
      await mongoose.connection.db.admin().ping();
      
      return {
        status: 'healthy',
        details: {
          state,
          host: mongoose.connection.host,
          port: mongoose.connection.port,
          database: mongoose.connection.name,
          uptime: process.uptime()
        }
      };
    } catch (error) {
      logger.error('Database health check failed', { error });
      return {
        status: 'unhealthy',
        details: {
          state: this.getConnectionState()
        }
      };
    }
  }
}

// Export singleton instance
export const mongoConnection = MongoDBConnection.getInstance();

// Helper function for easy connection
export const connectToMongoDB = async (): Promise<typeof mongoose> => {
  return mongoConnection.connect();
};

// Helper function for health check
export const checkMongoHealth = async () => {
  return mongoConnection.healthCheck();
};
