import { Router } from 'express';
import authRoutes from '../../routes/auth';
import userRoutes from '../../routes/users';
import { generalRateLimit } from '../../middleware/security/rate-limiting';

const router = Router();

// Apply general rate limiting to all API routes
router.use(generalRateLimit);

// Health check endpoint
router.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: process.env.API_VERSION || '1.0.0',
    environment: process.env.NODE_ENV || 'development'
  });
});

// Mount route modules
router.use('/auth', authRoutes);
router.use('/users', userRoutes);

// 404 handler for API routes
router.use('*', (req, res) => {
  res.status(404).json({
    error: 'API endpoint not found',
    code: 'ENDPOINT_NOT_FOUND',
    path: req.originalUrl,
    method: req.method
  });
});

export default router;
