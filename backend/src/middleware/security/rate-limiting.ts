import { Request, Response, NextFunction } from 'express';
import { RateLimiterMemory, RateLimiterRedis } from 'rate-limiter-flexible';
import { logger } from '../../utils/logger';
import { getClientIP } from '../../utils/request';

interface RateLimitOptions {
  windowMs: number;
  maxAttempts: number;
  blockDuration?: number;
  skipSuccessfulRequests?: boolean;
  keyGenerator?: (req: Request) => string;
  message?: string;
  statusCode?: number;
}

class RateLimitService {
  private limiters: Map<string, RateLimiterMemory | RateLimiterRedis> = new Map();

  public createLimiter(name: string, options: RateLimitOptions): RateLimiterMemory {
    const limiter = new RateLimiterMemory({
      points: options.maxAttempts,
      duration: Math.ceil(options.windowMs / 1000), // Convert to seconds
      blockDuration: options.blockDuration ? Math.ceil(options.blockDuration / 1000) : undefined,
      execEvenly: true
    });

    this.limiters.set(name, limiter);
    return limiter;
  }

  public getLimiter(name: string): RateLimiterMemory | RateLimiterRedis | undefined {
    return this.limiters.get(name);
  }

  public middleware(name: string, options: RateLimitOptions) {
    const limiter = this.createLimiter(name, options);

    return async (req: Request, res: Response, next: NextFunction) => {
      try {
        const key = options.keyGenerator ? options.keyGenerator(req) : getClientIP(req);
        
        await limiter.consume(key);
        next();
      } catch (rateLimiterRes) {
        const remainingPoints = rateLimiterRes.remainingPoints || 0;
        const msBeforeNext = rateLimiterRes.msBeforeNext || 0;
        const totalHits = rateLimiterRes.totalHits || 0;

        // Set rate limit headers
        res.set({
          'Retry-After': Math.round(msBeforeNext / 1000) || 1,
          'X-RateLimit-Limit': options.maxAttempts.toString(),
          'X-RateLimit-Remaining': Math.max(0, remainingPoints).toString(),
          'X-RateLimit-Reset': new Date(Date.now() + msBeforeNext).toISOString()
        });

        logger.warn('Rate limit exceeded', {
          key,
          endpoint: req.path,
          method: req.method,
          totalHits,
          remainingPoints,
          msBeforeNext
        });

        return res.status(options.statusCode || 429).json({
          error: options.message || 'Too many requests',
          code: 'RATE_LIMIT_EXCEEDED',
          retryAfter: Math.round(msBeforeNext / 1000) || 1
        });
      }
    };
  }
}

const rateLimitService = new RateLimitService();

// Pre-configured rate limiters for common use cases

// General API rate limiting
export const generalRateLimit = rateLimitService.middleware('general', {
  windowMs: 15 * 60 * 1000, // 15 minutes
  maxAttempts: 100,
  message: 'Too many requests from this IP'
});

// Authentication rate limiting
export const authRateLimit = rateLimitService.middleware('auth', {
  windowMs: 15 * 60 * 1000, // 15 minutes
  maxAttempts: 5,
  blockDuration: 15 * 60 * 1000, // Block for 15 minutes
  message: 'Too many authentication attempts',
  keyGenerator: (req: Request) => {
    // Use email if provided, otherwise IP
    const email = req.body?.email;
    return email ? `auth:${email}` : `auth:${getClientIP(req)}`;
  }
});

// Password reset rate limiting
export const passwordResetRateLimit = rateLimitService.middleware('password-reset', {
  windowMs: 60 * 60 * 1000, // 1 hour
  maxAttempts: 3,
  blockDuration: 60 * 60 * 1000, // Block for 1 hour
  message: 'Too many password reset attempts',
  keyGenerator: (req: Request) => {
    const email = req.body?.email;
    return email ? `reset:${email}` : `reset:${getClientIP(req)}`;
  }
});

// Registration rate limiting
export const registrationRateLimit = rateLimitService.middleware('registration', {
  windowMs: 60 * 60 * 1000, // 1 hour
  maxAttempts: 3,
  blockDuration: 60 * 60 * 1000, // Block for 1 hour
  message: 'Too many registration attempts',
  keyGenerator: (req: Request) => `register:${getClientIP(req)}`
});

// API key creation rate limiting
export const apiKeyCreationRateLimit = rateLimitService.middleware('api-key-creation', {
  windowMs: 60 * 60 * 1000, // 1 hour
  maxAttempts: 5,
  message: 'Too many API key creation attempts',
  keyGenerator: (req: Request) => {
    const userId = (req as any).user?.id;
    return userId ? `api-key:${userId}` : `api-key:${getClientIP(req)}`;
  }
});

// Profile update rate limiting
export const profileUpdateRateLimit = rateLimitService.middleware('profile-update', {
  windowMs: 5 * 60 * 1000, // 5 minutes
  maxAttempts: 10,
  message: 'Too many profile update attempts',
  keyGenerator: (req: Request) => {
    const userId = (req as any).user?.id;
    return userId ? `profile:${userId}` : `profile:${getClientIP(req)}`;
  }
});

// File upload rate limiting
export const fileUploadRateLimit = rateLimitService.middleware('file-upload', {
  windowMs: 15 * 60 * 1000, // 15 minutes
  maxAttempts: 20,
  message: 'Too many file upload attempts',
  keyGenerator: (req: Request) => {
    const userId = (req as any).user?.id;
    return userId ? `upload:${userId}` : `upload:${getClientIP(req)}`;
  }
});

// Strict rate limiting for sensitive operations
export const strictRateLimit = rateLimitService.middleware('strict', {
  windowMs: 60 * 60 * 1000, // 1 hour
  maxAttempts: 1,
  blockDuration: 60 * 60 * 1000, // Block for 1 hour
  message: 'Operation temporarily blocked due to rate limiting'
});

// Custom rate limiter factory
export function createCustomRateLimit(name: string, options: RateLimitOptions) {
  return rateLimitService.middleware(name, options);
}

// Helper to create user-specific rate limiter
export function createUserRateLimit(options: Omit<RateLimitOptions, 'keyGenerator'>) {
  return rateLimitService.middleware(`user-${Date.now()}`, {
    ...options,
    keyGenerator: (req: Request) => {
      const userId = (req as any).user?.id;
      return userId || getClientIP(req);
    }
  });
}

// Helper to create IP-based rate limiter
export function createIPRateLimit(options: Omit<RateLimitOptions, 'keyGenerator'>) {
  return rateLimitService.middleware(`ip-${Date.now()}`, {
    ...options,
    keyGenerator: (req: Request) => getClientIP(req)
  });
}

export { rateLimitService };
