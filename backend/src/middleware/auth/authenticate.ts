import { Request, Response, NextFunction } from 'express';
import { jwtStrategy } from '../../auth/strategies/jwt';
import { sessionService } from '../../services/auth/session';
import { User } from '../../models/users/User';
import { <PERSON>pi<PERSON><PERSON> } from '../../models/users/ApiKey';
import { AuthenticatedRequest, AuthContext } from '../../types/auth';
import { logger } from '../../utils/logger';
import { getClientIP, parseUserAgent } from '../../utils/request';

interface AuthOptions {
  required?: boolean;
  roles?: ('user' | 'admin')[];
  permissions?: string[];
}

export function authenticate(options: AuthOptions = { required: true }) {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      const authHeader = req.headers.authorization;
      const token = jwtStrategy.extractTokenFromHeader(authHeader);

      if (!token) {
        if (options.required) {
          return res.status(401).json({
            error: 'Authentication required',
            code: 'AUTH_TOKEN_MISSING'
          });
        }
        return next();
      }

      // Check if it's an API key token
      if (token.startsWith('lx_')) {
        return await handleApiKeyAuth(req as AuthenticatedRequest, res, next, token, options);
      }

      // Handle JWT token
      return await handleJWTAuth(req as AuthenticatedRequest, res, next, token, options);
    } catch (error) {
      logger.error('Authentication middleware error', { error });
      return res.status(500).json({
        error: 'Authentication failed',
        code: 'AUTH_ERROR'
      });
    }
  };
}

async function handleJWTAuth(
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction,
  token: string,
  options: AuthOptions
) {
  // Verify JWT token
  const payload = jwtStrategy.verifyAccessToken(token);
  if (!payload) {
    return res.status(401).json({
      error: 'Invalid or expired token',
      code: 'AUTH_TOKEN_INVALID'
    });
  }

  // Validate session
  const session = await sessionService.validateSession(payload.sessionId);
  if (!session) {
    return res.status(401).json({
      error: 'Session expired or invalid',
      code: 'AUTH_SESSION_INVALID'
    });
  }

  // Get user details
  const user = await User.findById(payload.userId);
  if (!user || user.status !== 'active') {
    return res.status(401).json({
      error: 'User account not found or inactive',
      code: 'AUTH_USER_INACTIVE'
    });
  }

  // Check role requirements
  if (options.roles && !options.roles.includes(user.role)) {
    return res.status(403).json({
      error: 'Insufficient permissions',
      code: 'AUTH_INSUFFICIENT_ROLE'
    });
  }

  // Set user context
  req.user = {
    id: user._id.toString(),
    email: user.profile.email,
    role: user.role,
    sessionId: session.id
  };

  // Set auth context for logging
  (req as any).authContext = {
    userId: user._id,
    email: user.profile.email,
    role: user.role,
    sessionId: session.id,
    ipAddress: getClientIP(req),
    userAgent: req.headers['user-agent'] || ''
  } as AuthContext;

  next();
}

async function handleApiKeyAuth(
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction,
  apiKey: string,
  options: AuthOptions
) {
  // Find and validate API key
  const keyDoc = await ApiKey.findByKey(apiKey);
  if (!keyDoc || !keyDoc.isActive || keyDoc.isExpired()) {
    return res.status(401).json({
      error: 'Invalid or expired API key',
      code: 'AUTH_API_KEY_INVALID'
    });
  }

  // Get user details
  const user = await User.findById(keyDoc.userId);
  if (!user || user.status !== 'active') {
    return res.status(401).json({
      error: 'API key owner account not found or inactive',
      code: 'AUTH_USER_INACTIVE'
    });
  }

  // Check role requirements
  if (options.roles && !options.roles.includes(user.role)) {
    return res.status(403).json({
      error: 'Insufficient permissions',
      code: 'AUTH_INSUFFICIENT_ROLE'
    });
  }

  // Check permission requirements
  if (options.permissions && !keyDoc.hasAnyPermission(options.permissions)) {
    return res.status(403).json({
      error: 'API key lacks required permissions',
      code: 'AUTH_INSUFFICIENT_PERMISSIONS',
      required: options.permissions,
      available: keyDoc.permissions
    });
  }

  // Update API key last used
  await keyDoc.updateLastUsed();

  // Set user context (API key authentication)
  req.user = {
    id: user._id.toString(),
    email: user.profile.email,
    role: user.role,
    sessionId: keyDoc._id.toString() // Use API key ID as session ID
  };

  // Set auth context for logging
  (req as any).authContext = {
    userId: user._id,
    email: user.profile.email,
    role: user.role,
    sessionId: keyDoc._id.toString(),
    ipAddress: getClientIP(req),
    userAgent: req.headers['user-agent'] || '',
    apiKeyId: keyDoc._id
  } as AuthContext & { apiKeyId: string };

  next();
}

// Middleware to require specific roles
export function requireRole(...roles: ('user' | 'admin')[]) {
  return authenticate({ required: true, roles });
}

// Middleware to require specific permissions (for API keys)
export function requirePermissions(...permissions: string[]) {
  return authenticate({ required: true, permissions });
}

// Middleware for optional authentication
export function optionalAuth() {
  return authenticate({ required: false });
}

// Middleware for admin only
export function requireAdmin() {
  return authenticate({ required: true, roles: ['admin'] });
}

// Helper to get auth context from request
export function getAuthContext(req: Request): AuthContext | null {
  return (req as any).authContext || null;
}

// Helper to check if user is authenticated
export function isAuthenticated(req: Request): boolean {
  return !!(req as AuthenticatedRequest).user;
}

// Helper to check if user has role
export function hasRole(req: Request, role: 'user' | 'admin'): boolean {
  const user = (req as AuthenticatedRequest).user;
  return user?.role === role;
}

// Helper to check if user is admin
export function isAdmin(req: Request): boolean {
  return hasRole(req, 'admin');
}
