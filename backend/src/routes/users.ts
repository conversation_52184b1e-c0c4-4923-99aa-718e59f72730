import { Router } from 'express';
import { UserProfileController } from '../controllers/users/profile';
import { SessionsController } from '../controllers/users/sessions';
import { ApiKeysController } from '../controllers/users/api-keys';
import { ActivityController } from '../controllers/users/activity';
import { authenticate } from '../middleware/auth/authenticate';
import { 
  profileUpdateRateLimit, 
  apiKeyCreationRateLimit 
} from '../middleware/security/rate-limiting';

const router = Router();
const profileController = new UserProfileController();
const sessionsController = new SessionsController();
const apiKeysController = new ApiKeysController();
const activityController = new ActivityController();

// All user routes require authentication
router.use(authenticate());

// Profile routes
router.get('/profile', profileController.getProfile.bind(profileController));
router.put('/profile', profileUpdateRateLimit, profileController.updateProfile.bind(profileController));
router.put('/security', profileUpdateRateLimit, profileController.updateSecurity.bind(profileController));
router.put('/preferences', profileUpdateRateLimit, profileController.updatePreferences.bind(profileController));
router.put('/notifications', profileUpdateRateLimit, profileController.updateNotifications.bind(profileController));

// Session management routes
router.get('/sessions', sessionsController.getSessions.bind(sessionsController));
router.delete('/sessions/:id', sessionsController.revokeSession.bind(sessionsController));
router.delete('/sessions', sessionsController.revokeAllSessions.bind(sessionsController));
router.get('/sessions/stats', sessionsController.getSessionStats.bind(sessionsController));

// API key management routes
router.get('/api-keys', apiKeysController.getApiKeys.bind(apiKeysController));
router.post('/api-keys', apiKeyCreationRateLimit, apiKeysController.createApiKey.bind(apiKeysController));
router.delete('/api-keys/:id', apiKeysController.revokeApiKey.bind(apiKeysController));
router.delete('/api-keys', apiKeysController.revokeAllApiKeys.bind(apiKeysController));
router.get('/api-keys/stats', apiKeysController.getApiKeyStats.bind(apiKeysController));
router.get('/api-keys/permissions', apiKeysController.getAvailablePermissions.bind(apiKeysController));

// Activity log routes
router.get('/activity', activityController.getActivity.bind(activityController));
router.get('/activity/stats', activityController.getActivityStats.bind(activityController));
router.get('/activity/security', activityController.getSecurityEvents.bind(activityController));
router.get('/activity/suspicious', activityController.getSuspiciousActivity.bind(activityController));

export default router;
