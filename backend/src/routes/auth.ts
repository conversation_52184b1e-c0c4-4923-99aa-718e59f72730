import { Router } from 'express';
import { AuthController } from '../controllers/auth/auth';
import { authenticate } from '../middleware/auth/authenticate';
import { 
  authRateLimit, 
  passwordResetRateLimit, 
  registrationRateLimit 
} from '../middleware/security/rate-limiting';

const router = Router();
const authController = new AuthController();

// Public routes with rate limiting
router.post('/register', registrationRateLimit, authController.register.bind(authController));
router.post('/login', authRateLimit, authController.login.bind(authController));
router.post('/refresh', authRateLimit, authController.refresh.bind(authController));

// Protected routes
router.post('/logout', authenticate(), authController.logout.bind(authController));

// Password reset routes (to be implemented)
// router.post('/forgot-password', passwordResetRateLimit, authController.forgotPassword.bind(authController));
// router.post('/reset-password', passwordResetRateLimit, authController.resetPassword.bind(authController));

// Email verification routes (to be implemented)
// router.post('/verify-email', authController.verifyEmail.bind(authController));
// router.post('/resend-verification', authRateLimit, authController.resendVerification.bind(authController));

export default router;
