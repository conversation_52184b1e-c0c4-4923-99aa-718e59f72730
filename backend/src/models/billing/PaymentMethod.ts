import mongoose, { Schema, Model } from 'mongoose';
import { PaymentMethodDocument } from '../../types/database/user';

const paymentMethodSchema = new Schema<PaymentMethodDocument>({
  userId: { 
    type: Schema.Types.ObjectId, 
    ref: 'User', 
    required: true,
    index: true 
  },
  type: { 
    type: String, 
    required: true,
    enum: ['credit-card', 'paypal', 'bank-account'],
    index: true
  },
  isDefault: { type: Boolean, default: false, index: true },
  details: {
    // Credit card details
    last4: { type: String },
    brand: { type: String }, // visa, mastercard, amex, etc.
    expiryMonth: { type: Number, min: 1, max: 12 },
    expiryYear: { type: Number },
    
    // PayPal details
    email: { type: String },
    
    // Bank account details
    bankName: { type: String },
    accountType: { type: String, enum: ['checking', 'savings'] }
  },
  gatewayId: { type: String }, // External payment gateway ID (Stripe, PayPal, etc.)
  isActive: { type: Boolean, default: true, index: true }
}, {
  timestamps: true,
  toJSON: {
    transform: function(doc, ret) {
      ret.id = ret._id;
      delete ret._id;
      delete ret.__v;
      delete ret.gatewayId; // Don't expose gateway IDs
      return ret;
    }
  }
});

// Compound indexes for performance
paymentMethodSchema.index({ userId: 1, isActive: 1 });
paymentMethodSchema.index({ userId: 1, isDefault: 1 });
paymentMethodSchema.index({ userId: 1, type: 1 });
paymentMethodSchema.index({ gatewayId: 1 });

// Instance methods
paymentMethodSchema.methods.setAsDefault = async function(): Promise<void> {
  // First, unset all other payment methods as default for this user
  await this.constructor.updateMany(
    { 
      userId: this.userId, 
      _id: { $ne: this._id },
      isActive: true 
    },
    { isDefault: false }
  );
  
  // Set this payment method as default
  this.isDefault = true;
  await this.save();
};

paymentMethodSchema.methods.deactivate = async function(): Promise<void> {
  this.isActive = false;
  
  // If this was the default payment method, find another active one to set as default
  if (this.isDefault) {
    this.isDefault = false;
    await this.save();
    
    const nextDefault = await this.constructor.findOne({
      userId: this.userId,
      _id: { $ne: this._id },
      isActive: true
    }).sort({ createdAt: 1 }); // Get the oldest active payment method
    
    if (nextDefault) {
      nextDefault.isDefault = true;
      await nextDefault.save();
    }
  } else {
    await this.save();
  }
};

paymentMethodSchema.methods.isExpired = function(): boolean {
  if (this.type === 'credit-card' && this.details.expiryMonth && this.details.expiryYear) {
    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth() + 1; // getMonth() returns 0-11
    
    return (
      this.details.expiryYear < currentYear ||
      (this.details.expiryYear === currentYear && this.details.expiryMonth < currentMonth)
    );
  }
  
  return false;
};

paymentMethodSchema.methods.getDisplayName = function(): string {
  switch (this.type) {
    case 'credit-card':
      return `${this.details.brand || 'Card'} •••• ${this.details.last4}`;
    case 'paypal':
      return this.details.email || 'PayPal Account';
    case 'bank-account':
      return `${this.details.bankName || 'Bank'} •••• ${this.details.last4}`;
    default:
      return 'Payment Method';
  }
};

// Static methods
paymentMethodSchema.statics.findActiveByUserId = function(userId: string) {
  return this.find({ 
    userId: new mongoose.Types.ObjectId(userId), 
    isActive: true 
  }).sort({ isDefault: -1, createdAt: -1 });
};

paymentMethodSchema.statics.findDefaultByUserId = function(userId: string) {
  return this.findOne({ 
    userId: new mongoose.Types.ObjectId(userId), 
    isDefault: true,
    isActive: true 
  });
};

paymentMethodSchema.statics.countActiveByUserId = function(userId: string) {
  return this.countDocuments({
    userId: new mongoose.Types.ObjectId(userId),
    isActive: true
  });
};

paymentMethodSchema.statics.findByGatewayId = function(gatewayId: string) {
  return this.findOne({ gatewayId, isActive: true });
};

paymentMethodSchema.statics.findExpiringCards = function(monthsAhead: number = 2) {
  const futureDate = new Date();
  futureDate.setMonth(futureDate.getMonth() + monthsAhead);
  
  const currentYear = new Date().getFullYear();
  const futureYear = futureDate.getFullYear();
  const futureMonth = futureDate.getMonth() + 1;
  
  return this.find({
    type: 'credit-card',
    isActive: true,
    $or: [
      {
        'details.expiryYear': { $lt: futureYear }
      },
      {
        'details.expiryYear': futureYear,
        'details.expiryMonth': { $lte: futureMonth }
      }
    ]
  }).populate('userId', 'profile.email profile.firstName profile.lastName');
};

// Validation
paymentMethodSchema.path('details').validate(function(details: any) {
  switch (this.type) {
    case 'credit-card':
      return details.last4 && details.brand && details.expiryMonth && details.expiryYear;
    case 'paypal':
      return details.email;
    case 'bank-account':
      return details.last4 && details.bankName && details.accountType;
    default:
      return false;
  }
}, 'Invalid payment method details for the specified type');

// Ensure only one default payment method per user
paymentMethodSchema.pre('save', async function(next) {
  if (this.isModified('isDefault') && this.isDefault) {
    // Unset other default payment methods for this user
    await this.constructor.updateMany(
      { 
        userId: this.userId, 
        _id: { $ne: this._id },
        isActive: true 
      },
      { isDefault: false }
    );
  }
  
  next();
});

// Pre-remove middleware
paymentMethodSchema.pre('deleteOne', { document: true, query: false }, async function(next) {
  // If removing the default payment method, set another one as default
  if (this.isDefault) {
    const nextDefault = await this.constructor.findOne({
      userId: this.userId,
      _id: { $ne: this._id },
      isActive: true
    }).sort({ createdAt: 1 });
    
    if (nextDefault) {
      nextDefault.isDefault = true;
      await nextDefault.save();
    }
  }
  
  next();
});

export const PaymentMethod: Model<PaymentMethodDocument> = mongoose.model<PaymentMethodDocument>('PaymentMethod', paymentMethodSchema);
