import mongoose, { Schema, Model } from 'mongoose';
import bcrypt from 'bcryptjs';
import crypto from 'crypto';
import { ApiKeyDocument } from '../../types/database/user';
import { API_PERMISSIONS } from '../../types/auth';

const apiKeySchema = new Schema<ApiKeyDocument>({
  userId: { 
    type: Schema.Types.ObjectId, 
    ref: 'User', 
    required: true,
    index: true 
  },
  name: { type: String, required: true, trim: true },
  keyHash: { type: String, required: true, unique: true },
  keyPrefix: { type: String, required: true }, // First 8 chars for display
  permissions: [{ 
    type: String, 
    enum: Object.keys(API_PERMISSIONS),
    required: true 
  }],
  lastUsedAt: { type: Date },
  expiresAt: { type: Date, index: { expireAfterSeconds: 0 } },
  isActive: { type: Boolean, default: true, index: true }
}, {
  timestamps: true,
  toJSON: {
    transform: function(doc, ret) {
      ret.id = ret._id;
      delete ret._id;
      delete ret.__v;
      delete ret.keyHash;
      return ret;
    }
  }
});

// Compound indexes for performance
apiKeySchema.index({ userId: 1, isActive: 1 });
apiKeySchema.index({ userId: 1, createdAt: -1 });
apiKeySchema.index({ keyHash: 1 });
apiKeySchema.index({ keyPrefix: 1 });

// Instance methods
apiKeySchema.methods.compareKey = async function(candidateKey: string): Promise<boolean> {
  return bcrypt.compare(candidateKey, this.keyHash);
};

apiKeySchema.methods.hashKey = async function(key: string): Promise<string> {
  const saltRounds = 12;
  return bcrypt.hash(key, saltRounds);
};

apiKeySchema.methods.updateLastUsed = async function(): Promise<void> {
  this.lastUsedAt = new Date();
  await this.save();
};

apiKeySchema.methods.deactivate = async function(): Promise<void> {
  this.isActive = false;
  await this.save();
};

apiKeySchema.methods.isExpired = function(): boolean {
  return this.expiresAt ? this.expiresAt < new Date() : false;
};

apiKeySchema.methods.hasPermission = function(permission: string): boolean {
  return this.permissions.includes(permission);
};

apiKeySchema.methods.hasAnyPermission = function(permissions: string[]): boolean {
  return permissions.some(permission => this.permissions.includes(permission));
};

// Static methods
apiKeySchema.statics.generateKey = function(): { key: string; prefix: string } {
  // Generate a secure random key
  const key = `lx_${crypto.randomBytes(32).toString('hex')}`;
  const prefix = key.substring(0, 12); // lx_ + first 8 chars
  
  return { key, prefix };
};

apiKeySchema.statics.findByKey = async function(key: string) {
  // Extract prefix to find potential matches
  const prefix = key.substring(0, 12);
  
  const apiKeys = await this.find({ 
    keyPrefix: prefix,
    isActive: true,
    $or: [
      { expiresAt: { $exists: false } },
      { expiresAt: { $gt: new Date() } }
    ]
  });
  
  // Compare full key hash
  for (const apiKey of apiKeys) {
    if (await apiKey.compareKey(key)) {
      return apiKey;
    }
  }
  
  return null;
};

apiKeySchema.statics.findActiveByUserId = function(userId: string) {
  return this.find({ 
    userId: new mongoose.Types.ObjectId(userId), 
    isActive: true 
  }).sort({ createdAt: -1 });
};

apiKeySchema.statics.countActiveByUserId = function(userId: string) {
  return this.countDocuments({
    userId: new mongoose.Types.ObjectId(userId),
    isActive: true,
    $or: [
      { expiresAt: { $exists: false } },
      { expiresAt: { $gt: new Date() } }
    ]
  });
};

apiKeySchema.statics.cleanupExpired = async function() {
  return this.updateMany(
    { 
      expiresAt: { $lt: new Date() },
      isActive: true 
    },
    { 
      isActive: false,
      updatedAt: new Date()
    }
  );
};

apiKeySchema.statics.revokeAllForUser = async function(userId: string, exceptKeyId?: string) {
  const query: any = { 
    userId: new mongoose.Types.ObjectId(userId),
    isActive: true 
  };
  
  if (exceptKeyId) {
    query._id = { $ne: new mongoose.Types.ObjectId(exceptKeyId) };
  }
  
  return this.updateMany(query, { 
    isActive: false,
    updatedAt: new Date()
  });
};

// Validation
apiKeySchema.path('permissions').validate(function(permissions: string[]) {
  if (!permissions || permissions.length === 0) {
    return false;
  }
  
  // Check if all permissions are valid
  const validPermissions = Object.keys(API_PERMISSIONS);
  return permissions.every(permission => validPermissions.includes(permission));
}, 'Invalid permissions specified');

// Pre-save middleware
apiKeySchema.pre('save', async function(next) {
  // Hash the key if it's new or modified
  if (this.isModified('keyHash') && !this.keyHash.startsWith('$2a$')) {
    const hashedKey = await this.hashKey(this.keyHash);
    this.keyHash = hashedKey;
  }
  
  next();
});

// Pre-remove middleware
apiKeySchema.pre('deleteOne', { document: true, query: false }, async function(next) {
  // Could add cleanup logic here if needed
  next();
});

export const ApiKey: Model<ApiKeyDocument> = mongoose.model<ApiKeyDocument>('ApiKey', apiKeySchema);
