import mongoose, { Schema, Model } from 'mongoose';
import bcrypt from 'bcryptjs';
import { UserSessionDocument } from '../../types/database/user';

const userSessionSchema = new Schema<UserSessionDocument>({
  userId: { 
    type: Schema.Types.ObjectId, 
    ref: 'User', 
    required: true,
    index: true 
  },
  deviceName: { type: String, required: true },
  browser: { type: String, required: true },
  ipAddress: { type: String, required: true, index: true },
  location: { type: String },
  userAgent: { type: String, required: true },
  refreshToken: { type: String, required: true, unique: true },
  refreshTokenHash: { type: String, required: true },
  loginAt: { type: Date, default: Date.now },
  lastActiveAt: { type: Date, default: Date.now },
  expiresAt: { type: Date, required: true, index: { expireAfterSeconds: 0 } },
  isActive: { type: Boolean, default: true, index: true }
}, {
  timestamps: true,
  toJSON: {
    transform: function(doc, ret) {
      ret.id = ret._id;
      delete ret._id;
      delete ret.__v;
      delete ret.refreshToken;
      delete ret.refreshTokenHash;
      return ret;
    }
  }
});

// Compound indexes for performance
userSessionSchema.index({ userId: 1, isActive: 1 });
userSessionSchema.index({ userId: 1, lastActiveAt: -1 });
userSessionSchema.index({ refreshTokenHash: 1 });
userSessionSchema.index({ expiresAt: 1 });

// Instance methods
userSessionSchema.methods.compareRefreshToken = async function(candidateToken: string): Promise<boolean> {
  return bcrypt.compare(candidateToken, this.refreshTokenHash);
};

userSessionSchema.methods.hashRefreshToken = async function(token: string): Promise<string> {
  const saltRounds = 10;
  return bcrypt.hash(token, saltRounds);
};

userSessionSchema.methods.updateActivity = async function(): Promise<void> {
  this.lastActiveAt = new Date();
  await this.save();
};

userSessionSchema.methods.deactivate = async function(): Promise<void> {
  this.isActive = false;
  await this.save();
};

userSessionSchema.methods.isExpired = function(): boolean {
  return this.expiresAt < new Date();
};

// Static methods
userSessionSchema.statics.findActiveByUserId = function(userId: string) {
  return this.find({ 
    userId: new mongoose.Types.ObjectId(userId), 
    isActive: true,
    expiresAt: { $gt: new Date() }
  }).sort({ lastActiveAt: -1 });
};

userSessionSchema.statics.findByRefreshToken = function(refreshToken: string) {
  return this.findOne({ 
    refreshToken,
    isActive: true,
    expiresAt: { $gt: new Date() }
  });
};

userSessionSchema.statics.deactivateAllForUser = async function(userId: string, exceptSessionId?: string) {
  const query: any = { 
    userId: new mongoose.Types.ObjectId(userId),
    isActive: true 
  };
  
  if (exceptSessionId) {
    query._id = { $ne: new mongoose.Types.ObjectId(exceptSessionId) };
  }
  
  return this.updateMany(query, { 
    isActive: false,
    updatedAt: new Date()
  });
};

userSessionSchema.statics.cleanupExpired = async function() {
  return this.deleteMany({
    $or: [
      { expiresAt: { $lt: new Date() } },
      { isActive: false, updatedAt: { $lt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) } } // 7 days old inactive sessions
    ]
  });
};

userSessionSchema.statics.getActiveSessionsCount = function(userId: string) {
  return this.countDocuments({
    userId: new mongoose.Types.ObjectId(userId),
    isActive: true,
    expiresAt: { $gt: new Date() }
  });
};

// Pre-save middleware
userSessionSchema.pre('save', async function(next) {
  // Hash refresh token if it's new or modified
  if (this.isModified('refreshToken')) {
    this.refreshTokenHash = await this.hashRefreshToken(this.refreshToken);
  }
  
  next();
});

// Pre-remove middleware to cleanup related data
userSessionSchema.pre('deleteOne', { document: true, query: false }, async function(next) {
  // Could add cleanup logic here if needed
  next();
});

export const UserSession: Model<UserSessionDocument> = mongoose.model<UserSessionDocument>('UserSession', userSessionSchema);
