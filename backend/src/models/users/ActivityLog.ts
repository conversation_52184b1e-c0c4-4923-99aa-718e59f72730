import mongoose, { Schema, Model } from 'mongoose';
import { ActivityLogDocument } from '../../types/database/user';

const activityLogSchema = new Schema<ActivityLogDocument>({
  userId: { 
    type: Schema.Types.ObjectId, 
    ref: 'User', 
    required: true,
    index: true 
  },
  type: { 
    type: String, 
    required: true,
    enum: [
      'login', 
      'logout', 
      'password_change', 
      'profile_update', 
      'payment', 
      'service_action', 
      'api_access', 
      'security_event'
    ],
    index: true
  },
  description: { type: String, required: true },
  details: { type: Schema.Types.Mixed },
  ipAddress: { type: String, required: true, index: true },
  userAgent: { type: String, required: true },
  location: { type: String },
  severity: { 
    type: String, 
    enum: ['low', 'medium', 'high', 'critical'], 
    default: 'low',
    index: true
  }
}, {
  timestamps: { createdAt: true, updatedAt: false }, // Only track creation time
  toJSON: {
    transform: function(doc, ret) {
      ret.id = ret._id;
      ret.timestamp = ret.createdAt;
      delete ret._id;
      delete ret.__v;
      delete ret.updatedAt;
      return ret;
    }
  }
});

// Compound indexes for efficient querying
activityLogSchema.index({ userId: 1, createdAt: -1 });
activityLogSchema.index({ userId: 1, type: 1, createdAt: -1 });
activityLogSchema.index({ userId: 1, severity: 1, createdAt: -1 });
activityLogSchema.index({ ipAddress: 1, createdAt: -1 });
activityLogSchema.index({ createdAt: -1 }); // For cleanup operations

// Static methods
activityLogSchema.statics.logActivity = async function(data: {
  userId: mongoose.Types.ObjectId;
  type: string;
  description: string;
  details?: Record<string, any>;
  ipAddress: string;
  userAgent: string;
  location?: string;
  severity?: 'low' | 'medium' | 'high' | 'critical';
}) {
  return this.create({
    userId: data.userId,
    type: data.type,
    description: data.description,
    details: data.details,
    ipAddress: data.ipAddress,
    userAgent: data.userAgent,
    location: data.location,
    severity: data.severity || 'low'
  });
};

activityLogSchema.statics.findByUserId = function(
  userId: string, 
  options: {
    type?: string;
    severity?: string;
    limit?: number;
    skip?: number;
    startDate?: Date;
    endDate?: Date;
  } = {}
) {
  const query: any = { userId: new mongoose.Types.ObjectId(userId) };
  
  if (options.type) {
    query.type = options.type;
  }
  
  if (options.severity) {
    query.severity = options.severity;
  }
  
  if (options.startDate || options.endDate) {
    query.createdAt = {};
    if (options.startDate) {
      query.createdAt.$gte = options.startDate;
    }
    if (options.endDate) {
      query.createdAt.$lte = options.endDate;
    }
  }
  
  let queryBuilder = this.find(query).sort({ createdAt: -1 });
  
  if (options.skip) {
    queryBuilder = queryBuilder.skip(options.skip);
  }
  
  if (options.limit) {
    queryBuilder = queryBuilder.limit(options.limit);
  }
  
  return queryBuilder;
};

activityLogSchema.statics.findSecurityEvents = function(
  userId: string,
  severity: 'medium' | 'high' | 'critical' = 'medium'
) {
  return this.find({
    userId: new mongoose.Types.ObjectId(userId),
    $or: [
      { type: 'security_event' },
      { type: 'login', severity: { $in: ['high', 'critical'] } },
      { type: 'password_change' }
    ],
    severity: { $in: severity === 'medium' ? ['medium', 'high', 'critical'] : 
                     severity === 'high' ? ['high', 'critical'] : ['critical'] }
  }).sort({ createdAt: -1 });
};

activityLogSchema.statics.findRecentByType = function(
  userId: string,
  type: string,
  hours: number = 24
) {
  const startTime = new Date(Date.now() - hours * 60 * 60 * 1000);
  
  return this.find({
    userId: new mongoose.Types.ObjectId(userId),
    type,
    createdAt: { $gte: startTime }
  }).sort({ createdAt: -1 });
};

activityLogSchema.statics.getActivityStats = async function(userId: string, days: number = 30) {
  const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);
  
  const stats = await this.aggregate([
    {
      $match: {
        userId: new mongoose.Types.ObjectId(userId),
        createdAt: { $gte: startDate }
      }
    },
    {
      $group: {
        _id: {
          type: '$type',
          severity: '$severity'
        },
        count: { $sum: 1 },
        lastActivity: { $max: '$createdAt' }
      }
    },
    {
      $group: {
        _id: '$_id.type',
        totalCount: { $sum: '$count' },
        severityBreakdown: {
          $push: {
            severity: '$_id.severity',
            count: '$count'
          }
        },
        lastActivity: { $max: '$lastActivity' }
      }
    }
  ]);
  
  return stats;
};

activityLogSchema.statics.cleanupOldLogs = async function(daysToKeep: number = 90) {
  const cutoffDate = new Date(Date.now() - daysToKeep * 24 * 60 * 60 * 1000);
  
  return this.deleteMany({
    createdAt: { $lt: cutoffDate },
    severity: { $in: ['low', 'medium'] } // Keep high and critical logs longer
  });
};

activityLogSchema.statics.findSuspiciousActivity = function(
  userId: string,
  hours: number = 24
) {
  const startTime = new Date(Date.now() - hours * 60 * 60 * 1000);
  
  return this.find({
    userId: new mongoose.Types.ObjectId(userId),
    createdAt: { $gte: startTime },
    $or: [
      { severity: { $in: ['high', 'critical'] } },
      { type: 'security_event' },
      { 
        type: 'login',
        'details.success': false
      }
    ]
  }).sort({ createdAt: -1 });
};

// TTL index for automatic cleanup of old logs (optional)
activityLogSchema.index(
  { createdAt: 1 }, 
  { 
    expireAfterSeconds: 365 * 24 * 60 * 60, // 1 year
    partialFilterExpression: { severity: { $in: ['low', 'medium'] } }
  }
);

export const ActivityLog: Model<ActivityLogDocument> = mongoose.model<ActivityLogDocument>('ActivityLog', activityLogSchema);
