import mongoose, { Schema, Model } from 'mongoose';
import bcrypt from 'bcryptjs';
import { UserDocument } from '../../types/database/user';

const securityQuestionSchema = new Schema({
  id: { type: String, required: true },
  question: { type: String, required: true },
  answerHash: { type: String, required: true }
}, { _id: false });

const companySchema = new Schema({
  name: { type: String, required: true },
  website: { type: String },
  industry: { type: String },
  size: { type: String }
}, { _id: false });

const addressSchema = new Schema({
  street: { type: String, required: true },
  city: { type: String, required: true },
  state: { type: String, required: true },
  zipCode: { type: String, required: true },
  country: { type: String, required: true }
}, { _id: false });

const profileSchema = new Schema({
  firstName: { type: String, required: true, trim: true },
  lastName: { type: String, required: true, trim: true },
  email: { type: String, required: true, unique: true, lowercase: true, trim: true },
  phone: { type: String, trim: true },
  avatar: { type: String },
  company: companySchema,
  address: { type: addressSchema, required: true },
  timezone: { type: String, default: 'America/New_York' },
  language: { type: String, default: 'en-US' },
  createdAt: { type: Date, default: Date.now },
  lastLoginAt: { type: Date }
}, { _id: false });

const securitySchema = new Schema({
  passwordHash: { type: String, required: true },
  passwordLastChanged: { type: Date, default: Date.now },
  twoFactorEnabled: { type: Boolean, default: false },
  twoFactorMethod: { type: String, enum: ['sms', 'app', 'email'] },
  twoFactorSecret: { type: String },
  backupCodes: [{ type: String }],
  securityQuestions: [securityQuestionSchema],
  loginNotifications: { type: Boolean, default: true },
  suspiciousActivityAlerts: { type: Boolean, default: true },
  failedLoginAttempts: { type: Number, default: 0 },
  lockedUntil: { type: Date }
}, { _id: false });

const billingSchema = new Schema({
  defaultPaymentMethod: { type: Schema.Types.ObjectId, ref: 'PaymentMethod' },
  billingAddress: { type: addressSchema, required: true },
  taxInformation: {
    taxId: { type: String },
    vatNumber: { type: String },
    taxExempt: { type: Boolean, default: false },
    taxExemptReason: { type: String }
  },
  invoicePreferences: {
    emailInvoices: { type: Boolean, default: true },
    paperInvoices: { type: Boolean, default: false },
    invoiceEmail: { type: String },
    autoPayEnabled: { type: Boolean, default: false },
    paymentTerms: { type: Number, default: 15 }
  },
  subscriptionPreferences: {
    autoRenew: { type: Boolean, default: true },
    renewalReminders: { type: Boolean, default: true },
    upgradeNotifications: { type: Boolean, default: true }
  }
}, { _id: false });

const notificationsSchema = new Schema({
  email: {
    serviceUpdates: { type: Boolean, default: true },
    maintenanceAlerts: { type: Boolean, default: true },
    billingReminders: { type: Boolean, default: true },
    invoiceNotifications: { type: Boolean, default: true },
    ticketUpdates: { type: Boolean, default: true },
    securityAlerts: { type: Boolean, default: true },
    marketingEmails: { type: Boolean, default: false },
    weeklyReports: { type: Boolean, default: true }
  },
  sms: {
    enabled: { type: Boolean, default: false },
    phoneNumber: { type: String },
    criticalAlerts: { type: Boolean, default: true },
    serviceDowntime: { type: Boolean, default: true },
    billingIssues: { type: Boolean, default: true },
    securityAlerts: { type: Boolean, default: true }
  },
  push: {
    enabled: { type: Boolean, default: true },
    serviceUpdates: { type: Boolean, default: true },
    ticketResponses: { type: Boolean, default: true },
    billingReminders: { type: Boolean, default: false }
  },
  frequency: {
    digest: { type: String, enum: ['immediate', 'hourly', 'daily', 'weekly'], default: 'daily' },
    quietHours: {
      enabled: { type: Boolean, default: true },
      start: { type: String, default: '22:00' },
      end: { type: String, default: '08:00' }
    }
  }
}, { _id: false });

const preferencesSchema = new Schema({
  theme: { type: String, enum: ['dark', 'light', 'auto'], default: 'dark' },
  compactMode: { type: Boolean, default: false },
  showWelcomeMessage: { type: Boolean, default: true },
  defaultDashboardView: { 
    type: String, 
    enum: ['overview', 'services', 'tickets', 'invoices'], 
    default: 'overview' 
  },
  widgetPreferences: {
    showStats: { type: Boolean, default: true },
    showRecentActivity: { type: Boolean, default: true },
    showQuickActions: { type: Boolean, default: true },
    showSystemStatus: { type: Boolean, default: true }
  },
  tablePreferences: {
    itemsPerPage: { type: Number, default: 25 },
    defaultSort: { type: String, default: 'created_desc' },
    compactTables: { type: Boolean, default: false }
  }
}, { _id: false });

const userSchema = new Schema<UserDocument>({
  profile: { type: profileSchema, required: true },
  security: { type: securitySchema, required: true },
  billing: { type: billingSchema, required: true },
  notifications: { type: notificationsSchema, required: true },
  preferences: { type: preferencesSchema, required: true },
  role: { type: String, enum: ['user', 'admin'], default: 'user' },
  status: { type: String, enum: ['active', 'suspended', 'pending'], default: 'pending' },
  emailVerified: { type: Boolean, default: false },
  emailVerificationToken: { type: String },
  passwordResetToken: { type: String },
  passwordResetExpires: { type: Date }
}, {
  timestamps: true,
  toJSON: {
    transform: function(doc, ret) {
      ret.id = ret._id;
      delete ret._id;
      delete ret.__v;
      delete ret.security.passwordHash;
      delete ret.security.twoFactorSecret;
      delete ret.emailVerificationToken;
      delete ret.passwordResetToken;
      delete ret.passwordResetExpires;
      return ret;
    }
  }
});

// Indexes for performance
userSchema.index({ 'profile.email': 1 }, { unique: true });
userSchema.index({ emailVerificationToken: 1 });
userSchema.index({ passwordResetToken: 1 });
userSchema.index({ status: 1 });
userSchema.index({ role: 1 });
userSchema.index({ createdAt: -1 });

// Instance methods
userSchema.methods.comparePassword = async function(candidatePassword: string): Promise<boolean> {
  return bcrypt.compare(candidatePassword, this.security.passwordHash);
};

userSchema.methods.hashPassword = async function(password: string): Promise<string> {
  const saltRounds = 12;
  return bcrypt.hash(password, saltRounds);
};

userSchema.methods.incrementFailedLogins = async function(): Promise<void> {
  this.security.failedLoginAttempts += 1;
  
  // Lock account after 5 failed attempts for 30 minutes
  if (this.security.failedLoginAttempts >= 5) {
    this.security.lockedUntil = new Date(Date.now() + 30 * 60 * 1000);
  }
  
  await this.save();
};

userSchema.methods.resetFailedLogins = async function(): Promise<void> {
  this.security.failedLoginAttempts = 0;
  this.security.lockedUntil = undefined;
  await this.save();
};

userSchema.methods.isLocked = function(): boolean {
  return !!(this.security.lockedUntil && this.security.lockedUntil > new Date());
};

// Static methods
userSchema.statics.findByEmail = function(email: string) {
  return this.findOne({ 'profile.email': email.toLowerCase() });
};

userSchema.statics.findActiveUsers = function() {
  return this.find({ status: 'active' });
};

// Pre-save middleware
userSchema.pre('save', async function(next) {
  // Hash password if it's modified
  if (this.isModified('security.passwordHash') && !this.security.passwordHash.startsWith('$2a$')) {
    this.security.passwordHash = await this.hashPassword(this.security.passwordHash);
    this.security.passwordLastChanged = new Date();
  }
  
  // Set email as verified if user is being activated
  if (this.isModified('status') && this.status === 'active') {
    this.emailVerified = true;
  }
  
  next();
});

export const User: Model<UserDocument> = mongoose.model<UserDocument>('User', userSchema);
