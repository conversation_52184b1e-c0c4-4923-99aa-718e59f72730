import { RegisterRequest, LoginRequest } from '../../types/auth';
import { validateEmail, validatePassword } from '../../utils/request';

interface ValidationResult {
  isValid: boolean;
  errors: Record<string, string>;
}

export function validateRegistration(data: RegisterRequest): ValidationResult {
  const errors: Record<string, string> = {};

  // Validate first name
  if (!data.firstName || data.firstName.trim().length < 2) {
    errors.firstName = 'First name must be at least 2 characters long';
  }

  // Validate last name
  if (!data.lastName || data.lastName.trim().length < 2) {
    errors.lastName = 'Last name must be at least 2 characters long';
  }

  // Validate email
  if (!data.email) {
    errors.email = 'Email is required';
  } else if (!validateEmail(data.email)) {
    errors.email = 'Please enter a valid email address';
  }

  // Validate password
  if (!data.password) {
    errors.password = 'Password is required';
  } else {
    const passwordValidation = validatePassword(data.password);
    if (!passwordValidation.isValid) {
      errors.password = passwordValidation.errors.join('. ');
    }
  }

  // Validate password confirmation
  if (!data.confirmPassword) {
    errors.confirmPassword = 'Password confirmation is required';
  } else if (data.password !== data.confirmPassword) {
    errors.confirmPassword = 'Passwords do not match';
  }

  // Validate address
  if (!data.address) {
    errors.address = 'Address is required';
  } else {
    if (!data.address.street || data.address.street.trim().length < 5) {
      errors['address.street'] = 'Street address must be at least 5 characters long';
    }
    if (!data.address.city || data.address.city.trim().length < 2) {
      errors['address.city'] = 'City must be at least 2 characters long';
    }
    if (!data.address.state || data.address.state.trim().length < 2) {
      errors['address.state'] = 'State/Province must be at least 2 characters long';
    }
    if (!data.address.zipCode || data.address.zipCode.trim().length < 3) {
      errors['address.zipCode'] = 'ZIP/Postal code must be at least 3 characters long';
    }
    if (!data.address.country || data.address.country.trim().length < 2) {
      errors['address.country'] = 'Country must be at least 2 characters long';
    }
  }

  // Validate company (optional)
  if (data.company && data.company.name) {
    if (data.company.name.trim().length < 2) {
      errors['company.name'] = 'Company name must be at least 2 characters long';
    }
    if (data.company.website && !isValidUrl(data.company.website)) {
      errors['company.website'] = 'Please enter a valid website URL';
    }
  }

  // Validate terms acceptance
  if (!data.acceptTerms) {
    errors.acceptTerms = 'You must accept the terms and conditions';
  }

  // Validate timezone (optional)
  if (data.timezone && !isValidTimezone(data.timezone)) {
    errors.timezone = 'Invalid timezone';
  }

  // Validate language (optional)
  if (data.language && !isValidLanguage(data.language)) {
    errors.language = 'Invalid language code';
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
}

export function validateLogin(data: LoginRequest): ValidationResult {
  const errors: Record<string, string> = {};

  // Validate email
  if (!data.email) {
    errors.email = 'Email is required';
  } else if (!validateEmail(data.email)) {
    errors.email = 'Please enter a valid email address';
  }

  // Validate password
  if (!data.password) {
    errors.password = 'Password is required';
  } else if (data.password.length < 1) {
    errors.password = 'Password cannot be empty';
  }

  // Validate device name (optional)
  if (data.deviceName && data.deviceName.length > 100) {
    errors.deviceName = 'Device name is too long';
  }

  // Validate 2FA code (optional)
  if (data.twoFactorCode) {
    if (!/^\d{6}$/.test(data.twoFactorCode)) {
      errors.twoFactorCode = 'Two-factor code must be 6 digits';
    }
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
}

function isValidUrl(url: string): boolean {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

function isValidTimezone(timezone: string): boolean {
  const validTimezones = [
    'America/New_York',
    'America/Chicago',
    'America/Denver',
    'America/Los_Angeles',
    'Europe/London',
    'Europe/Paris',
    'Europe/Berlin',
    'Asia/Tokyo',
    'Asia/Shanghai',
    'Australia/Sydney',
    'UTC'
  ];
  return validTimezones.includes(timezone);
}

function isValidLanguage(language: string): boolean {
  const validLanguages = [
    'en-US',
    'en-GB',
    'es-ES',
    'fr-FR',
    'de-DE',
    'it-IT',
    'pt-BR',
    'ja-JP',
    'zh-CN',
    'ko-KR'
  ];
  return validLanguages.includes(language);
}
