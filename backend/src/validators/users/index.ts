import { validateEmail } from '../../utils/request';

interface ValidationResult {
  isValid: boolean;
  errors: Record<string, string>;
}

export function validateProfileUpdate(data: any): ValidationResult {
  const errors: Record<string, string> = {};

  // Validate first name
  if (data.firstName !== undefined) {
    if (!data.firstName || data.firstName.trim().length < 2) {
      errors.firstName = 'First name must be at least 2 characters long';
    }
  }

  // Validate last name
  if (data.lastName !== undefined) {
    if (!data.lastName || data.lastName.trim().length < 2) {
      errors.lastName = 'Last name must be at least 2 characters long';
    }
  }

  // Validate phone (optional)
  if (data.phone !== undefined && data.phone) {
    if (!/^[\+]?[1-9][\d]{0,15}$/.test(data.phone.replace(/[\s\-\(\)]/g, ''))) {
      errors.phone = 'Please enter a valid phone number';
    }
  }

  // Validate company information (optional)
  if (data.company) {
    if (data.company.name && data.company.name.trim().length < 2) {
      errors['company.name'] = 'Company name must be at least 2 characters long';
    }
    if (data.company.website && !isValidUrl(data.company.website)) {
      errors['company.website'] = 'Please enter a valid website URL';
    }
    if (data.company.industry && data.company.industry.trim().length < 2) {
      errors['company.industry'] = 'Industry must be at least 2 characters long';
    }
    if (data.company.size && !isValidCompanySize(data.company.size)) {
      errors['company.size'] = 'Invalid company size';
    }
  }

  // Validate address
  if (data.address) {
    if (data.address.street && data.address.street.trim().length < 5) {
      errors['address.street'] = 'Street address must be at least 5 characters long';
    }
    if (data.address.city && data.address.city.trim().length < 2) {
      errors['address.city'] = 'City must be at least 2 characters long';
    }
    if (data.address.state && data.address.state.trim().length < 2) {
      errors['address.state'] = 'State/Province must be at least 2 characters long';
    }
    if (data.address.zipCode && data.address.zipCode.trim().length < 3) {
      errors['address.zipCode'] = 'ZIP/Postal code must be at least 3 characters long';
    }
    if (data.address.country && data.address.country.trim().length < 2) {
      errors['address.country'] = 'Country must be at least 2 characters long';
    }
  }

  // Validate timezone
  if (data.timezone !== undefined && data.timezone && !isValidTimezone(data.timezone)) {
    errors.timezone = 'Invalid timezone';
  }

  // Validate language
  if (data.language !== undefined && data.language && !isValidLanguage(data.language)) {
    errors.language = 'Invalid language code';
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
}

export function validateSecurityUpdate(data: any): ValidationResult {
  const errors: Record<string, string> = {};

  // Validate boolean fields
  if (data.loginNotifications !== undefined && typeof data.loginNotifications !== 'boolean') {
    errors.loginNotifications = 'Login notifications must be true or false';
  }

  if (data.suspiciousActivityAlerts !== undefined && typeof data.suspiciousActivityAlerts !== 'boolean') {
    errors.suspiciousActivityAlerts = 'Suspicious activity alerts must be true or false';
  }

  if (data.twoFactorEnabled !== undefined && typeof data.twoFactorEnabled !== 'boolean') {
    errors.twoFactorEnabled = 'Two-factor authentication must be true or false';
  }

  // Validate 2FA method if 2FA is being enabled
  if (data.twoFactorEnabled === true && data.twoFactorMethod) {
    if (!['sms', 'app', 'email'].includes(data.twoFactorMethod)) {
      errors.twoFactorMethod = 'Two-factor method must be sms, app, or email';
    }
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
}

export function validatePreferencesUpdate(data: any): ValidationResult {
  const errors: Record<string, string> = {};

  // Validate theme
  if (data.theme !== undefined && !['dark', 'light', 'auto'].includes(data.theme)) {
    errors.theme = 'Theme must be dark, light, or auto';
  }

  // Validate boolean preferences
  const booleanFields = [
    'compactMode',
    'showWelcomeMessage'
  ];

  booleanFields.forEach(field => {
    if (data[field] !== undefined && typeof data[field] !== 'boolean') {
      errors[field] = `${field} must be true or false`;
    }
  });

  // Validate default dashboard view
  if (data.defaultDashboardView !== undefined) {
    const validViews = ['overview', 'services', 'tickets', 'invoices'];
    if (!validViews.includes(data.defaultDashboardView)) {
      errors.defaultDashboardView = 'Invalid dashboard view';
    }
  }

  // Validate widget preferences
  if (data.widgetPreferences) {
    const widgetFields = [
      'showStats',
      'showRecentActivity',
      'showQuickActions',
      'showSystemStatus'
    ];

    widgetFields.forEach(field => {
      if (data.widgetPreferences[field] !== undefined && typeof data.widgetPreferences[field] !== 'boolean') {
        errors[`widgetPreferences.${field}`] = `${field} must be true or false`;
      }
    });
  }

  // Validate table preferences
  if (data.tablePreferences) {
    if (data.tablePreferences.itemsPerPage !== undefined) {
      const itemsPerPage = parseInt(data.tablePreferences.itemsPerPage);
      if (isNaN(itemsPerPage) || itemsPerPage < 10 || itemsPerPage > 100) {
        errors['tablePreferences.itemsPerPage'] = 'Items per page must be between 10 and 100';
      }
    }

    if (data.tablePreferences.defaultSort !== undefined) {
      const validSorts = ['created_desc', 'created_asc', 'name_asc', 'name_desc', 'status_asc'];
      if (!validSorts.includes(data.tablePreferences.defaultSort)) {
        errors['tablePreferences.defaultSort'] = 'Invalid sort option';
      }
    }

    if (data.tablePreferences.compactTables !== undefined && typeof data.tablePreferences.compactTables !== 'boolean') {
      errors['tablePreferences.compactTables'] = 'Compact tables must be true or false';
    }
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
}

export function validateNotificationUpdate(data: any): ValidationResult {
  const errors: Record<string, string> = {};

  // Validate email notifications
  if (data.email) {
    const emailFields = [
      'serviceUpdates',
      'maintenanceAlerts',
      'billingReminders',
      'invoiceNotifications',
      'ticketUpdates',
      'securityAlerts',
      'marketingEmails',
      'weeklyReports'
    ];

    emailFields.forEach(field => {
      if (data.email[field] !== undefined && typeof data.email[field] !== 'boolean') {
        errors[`email.${field}`] = `${field} must be true or false`;
      }
    });
  }

  // Validate SMS notifications
  if (data.sms) {
    if (data.sms.enabled !== undefined && typeof data.sms.enabled !== 'boolean') {
      errors['sms.enabled'] = 'SMS enabled must be true or false';
    }

    if (data.sms.phoneNumber !== undefined && data.sms.phoneNumber) {
      if (!/^[\+]?[1-9][\d]{0,15}$/.test(data.sms.phoneNumber.replace(/[\s\-\(\)]/g, ''))) {
        errors['sms.phoneNumber'] = 'Please enter a valid phone number';
      }
    }

    const smsFields = ['criticalAlerts', 'serviceDowntime', 'billingIssues', 'securityAlerts'];
    smsFields.forEach(field => {
      if (data.sms[field] !== undefined && typeof data.sms[field] !== 'boolean') {
        errors[`sms.${field}`] = `${field} must be true or false`;
      }
    });
  }

  // Validate push notifications
  if (data.push) {
    const pushFields = ['enabled', 'serviceUpdates', 'ticketResponses', 'billingReminders'];
    pushFields.forEach(field => {
      if (data.push[field] !== undefined && typeof data.push[field] !== 'boolean') {
        errors[`push.${field}`] = `${field} must be true or false`;
      }
    });
  }

  // Validate frequency settings
  if (data.frequency) {
    if (data.frequency.digest !== undefined) {
      const validDigests = ['immediate', 'hourly', 'daily', 'weekly'];
      if (!validDigests.includes(data.frequency.digest)) {
        errors['frequency.digest'] = 'Invalid digest frequency';
      }
    }

    if (data.frequency.quietHours) {
      if (data.frequency.quietHours.enabled !== undefined && typeof data.frequency.quietHours.enabled !== 'boolean') {
        errors['frequency.quietHours.enabled'] = 'Quiet hours enabled must be true or false';
      }

      if (data.frequency.quietHours.start !== undefined && !isValidTime(data.frequency.quietHours.start)) {
        errors['frequency.quietHours.start'] = 'Invalid start time format (use HH:MM)';
      }

      if (data.frequency.quietHours.end !== undefined && !isValidTime(data.frequency.quietHours.end)) {
        errors['frequency.quietHours.end'] = 'Invalid end time format (use HH:MM)';
      }
    }
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
}

// Helper functions
function isValidUrl(url: string): boolean {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

function isValidCompanySize(size: string): boolean {
  const validSizes = [
    '1-10 employees',
    '11-50 employees',
    '51-200 employees',
    '201-500 employees',
    '500+ employees'
  ];
  return validSizes.includes(size);
}

function isValidTimezone(timezone: string): boolean {
  const validTimezones = [
    'America/New_York',
    'America/Chicago',
    'America/Denver',
    'America/Los_Angeles',
    'Europe/London',
    'Europe/Paris',
    'Europe/Berlin',
    'Asia/Tokyo',
    'Asia/Shanghai',
    'Australia/Sydney',
    'UTC'
  ];
  return validTimezones.includes(timezone);
}

function isValidLanguage(language: string): boolean {
  const validLanguages = [
    'en-US',
    'en-GB',
    'es-ES',
    'fr-FR',
    'de-DE',
    'it-IT',
    'pt-BR',
    'ja-JP',
    'zh-CN',
    'ko-KR'
  ];
  return validLanguages.includes(language);
}

function isValidTime(time: string): boolean {
  return /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.test(time);
}
