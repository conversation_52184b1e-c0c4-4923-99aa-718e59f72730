# Environment Configuration
NODE_ENV=development
PORT=3001
API_VERSION=1.0.0

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/luminexclient

# JWT Configuration
JWT_ACCESS_SECRET=your-super-secret-access-token-key-here
JWT_REFRESH_SECRET=your-super-secret-refresh-token-key-here
JWT_ACCESS_EXPIRY=15m
JWT_REFRESH_EXPIRY=7d

# Frontend Configuration
FRONTEND_URL=http://localhost:3000

# Logging Configuration
LOG_LEVEL=INFO

# Rate Limiting Configuration
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Email Configuration (Optional - for future implementation)
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USER=<EMAIL>
# SMTP_PASS=your-app-password
# FROM_EMAIL=<EMAIL>
# FROM_NAME=LuminexClient

# File Upload Configuration (Optional)
# MAX_FILE_SIZE=10485760
# UPLOAD_PATH=./storage/uploads

# Redis Configuration (Optional - for advanced rate limiting)
# REDIS_URL=redis://localhost:6379

# Security Configuration
# BCRYPT_ROUNDS=12
# SESSION_SECRET=your-session-secret-here

# External Services (Optional)
# GEOLOCATION_API_KEY=your-geolocation-api-key
# PAYMENT_GATEWAY_KEY=your-payment-gateway-key
