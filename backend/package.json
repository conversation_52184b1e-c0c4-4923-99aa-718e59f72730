{"name": "luminexclient-backend", "version": "1.0.0", "description": "Backend API for LuminexClient - Open Source WHMCS Alternative", "main": "dist/server.js", "scripts": {"dev": "nodemon src/server.ts", "build": "tsc", "start": "node dist/server.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "clean": "<PERSON><PERSON><PERSON> dist", "prebuild": "npm run clean", "postbuild": "cp package.json dist/", "db:seed": "ts-node scripts/database/seed.ts", "db:migrate": "ts-node scripts/database/migrate.ts"}, "keywords": ["whmcs", "hosting", "billing", "client-management", "api", "typescript", "mongodb"], "author": "LuminexClient Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "mongoose": "^7.5.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "helmet": "^7.0.0", "compression": "^1.7.4", "rate-limiter-flexible": "^3.0.8", "dotenv": "^16.3.1"}, "devDependencies": {"@types/express": "^4.17.17", "@types/node": "^20.5.0", "@types/bcryptjs": "^2.4.2", "@types/jsonwebtoken": "^9.0.2", "@types/cors": "^2.8.13", "@types/compression": "^1.7.2", "@types/jest": "^29.5.4", "typescript": "^5.1.6", "nodemon": "^3.0.1", "ts-node": "^10.9.1", "jest": "^29.6.2", "ts-jest": "^29.1.1", "eslint": "^8.47.0", "@typescript-eslint/eslint-plugin": "^6.4.0", "@typescript-eslint/parser": "^6.4.0", "rimraf": "^5.0.1"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}