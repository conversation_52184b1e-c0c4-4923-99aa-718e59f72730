# LuminexClient Backend API

Backend API for LuminexClient - An open-source WHMCS alternative for hosting providers.

## 🚀 Features

### Authentication & Security
- JWT-based authentication with refresh tokens
- Session management with device tracking
- API key management with scoped permissions
- Two-factor authentication support
- Rate limiting and security middleware
- Activity logging and suspicious activity detection

### User Management
- Comprehensive user profiles with company information
- Security settings and preferences
- Notification preferences (email, SMS, push)
- Dashboard customization settings
- Billing information and payment methods

### Database
- MongoDB with Mongoose ODM
- Optimized indexes for performance
- Comprehensive data models
- Activity logging and audit trails

## 📋 Prerequisites

- Node.js 18+ 
- MongoDB 4.4+
- npm 8+

## 🛠️ Installation

1. **Clone and navigate to backend directory**
```bash
cd backend
```

2. **Install dependencies**
```bash
npm install
```

3. **Environment setup**
```bash
cp .env.example .env
# Edit .env with your configuration
```

4. **Start MongoDB**
```bash
# Using Docker
docker run -d -p 27017:27017 --name mongodb mongo:latest

# Or use your local MongoDB installation
```

5. **Start development server**
```bash
npm run dev
```

## 🔧 Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `NODE_ENV` | Environment mode | `development` |
| `PORT` | Server port | `3001` |
| `MONGODB_URI` | MongoDB connection string | `mongodb://localhost:27017/luminexclient` |
| `JWT_ACCESS_SECRET` | JWT access token secret | Required |
| `JWT_REFRESH_SECRET` | JWT refresh token secret | Required |
| `FRONTEND_URL` | Frontend URL for CORS | `http://localhost:3000` |

## 📚 API Documentation

### Authentication Endpoints

#### POST `/api/v1/auth/register`
Register a new user account.

#### POST `/api/v1/auth/login`
Authenticate user and create session.

#### POST `/api/v1/auth/refresh`
Refresh access token using refresh token.

#### POST `/api/v1/auth/logout`
Logout and invalidate session.

### User Management Endpoints

#### GET `/api/v1/users/profile`
Get current user profile.

#### PUT `/api/v1/users/profile`
Update user profile information.

#### PUT `/api/v1/users/security`
Update security settings.

#### PUT `/api/v1/users/preferences`
Update dashboard preferences.

#### PUT `/api/v1/users/notifications`
Update notification preferences.

### Session Management

#### GET `/api/v1/users/sessions`
Get active user sessions.

#### DELETE `/api/v1/users/sessions/:id`
Revoke specific session.

#### DELETE `/api/v1/users/sessions`
Revoke all sessions.

### API Key Management

#### GET `/api/v1/users/api-keys`
Get user API keys.

#### POST `/api/v1/users/api-keys`
Create new API key.

#### DELETE `/api/v1/users/api-keys/:id`
Revoke API key.

### Activity Logs

#### GET `/api/v1/users/activity`
Get user activity logs with filtering.

#### GET `/api/v1/users/activity/stats`
Get activity statistics.

#### GET `/api/v1/users/activity/security`
Get security events.

## 🏗️ Project Structure

```
backend/
├── src/
│   ├── api/v1/           # API version 1 routes
│   ├── auth/             # Authentication strategies
│   ├── controllers/      # Request handlers
│   ├── database/         # Database connections
│   ├── middleware/       # Express middleware
│   ├── models/           # Database models
│   ├── routes/           # Route definitions
│   ├── services/         # Business logic services
│   ├── types/            # TypeScript type definitions
│   ├── utils/            # Utility functions
│   ├── validators/       # Input validation
│   └── server.ts         # Main server file
├── storage/              # File storage
├── templates/            # Email templates
├── tests/                # Test files
└── scripts/              # Database scripts
```

## 🔒 Security Features

- **Rate Limiting**: Configurable rate limits for different endpoints
- **Input Validation**: Comprehensive input sanitization and validation
- **Password Security**: bcrypt hashing with configurable rounds
- **Session Security**: Secure session management with device tracking
- **API Security**: Scoped API keys with permission management
- **Activity Monitoring**: Comprehensive logging and suspicious activity detection

## 🧪 Testing

```bash
# Run tests
npm test

# Run tests in watch mode
npm run test:watch

# Run with coverage
npm run test:coverage
```

## 📦 Building for Production

```bash
# Build the application
npm run build

# Start production server
npm start
```

## 🔄 Database Management

```bash
# Run database migrations
npm run db:migrate

# Seed database with sample data
npm run db:seed
```

## 📊 Monitoring & Logging

The API includes comprehensive logging with different levels:
- `DEBUG`: Detailed debugging information
- `INFO`: General information about application flow
- `WARN`: Warning messages for potential issues
- `ERROR`: Error messages for failures

Set `LOG_LEVEL` environment variable to control logging verbosity.

## 🤝 Integration with Frontend

This backend is designed to work seamlessly with the LuminexClient Remix frontend:

1. **CORS Configuration**: Properly configured for frontend domain
2. **Data Format**: Returns data in exact format expected by frontend components
3. **Error Handling**: Consistent error responses matching frontend error handling
4. **Authentication**: JWT tokens work with frontend auth system

## 🚀 Deployment

### Docker Deployment
```bash
# Build Docker image
docker build -t luminexclient-backend .

# Run container
docker run -p 3001:3001 --env-file .env luminexclient-backend
```

### Production Considerations
- Use environment variables for all configuration
- Set up proper MongoDB replica set for production
- Configure reverse proxy (nginx) for SSL termination
- Set up monitoring and logging aggregation
- Configure backup strategies for database

## 🔧 Development

```bash
# Start development server with hot reload
npm run dev

# Lint code
npm run lint

# Fix linting issues
npm run lint:fix
```

## 📝 License

MIT License - see LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create feature branch
3. Make changes with tests
4. Submit pull request

## 📞 Support

For support and questions:
- Create an issue on GitHub
- Check documentation
- Review API endpoints and examples
